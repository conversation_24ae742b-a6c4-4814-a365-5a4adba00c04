'''
Author: mobolun <EMAIL>
Date: 2025-07-25 01:16:54
LastEditors: mobolun <EMAIL>
LastEditTime: 2025-07-25 23:14:27
FilePath: \VedioLocal\srt_parser.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
# -*- coding: utf-8 -*-

import re
import logging
from datetime import timedelta

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SubtitleEntry:
    """代表一个字幕条目的类"""
    def __init__(self, index, start, end, content):
        self.index = int(index)
        self.start_time = self._parse_time(start)
        self.end_time = self._parse_time(end)
        self.content = content.strip()

    def _parse_time(self, time_str: str) -> timedelta:
        """将SRT时间格式 (HH:MM:SS,ms) 转换为 timedelta 对象"""
        parts = re.split(r'[:,]', time_str)
        return timedelta(
            hours=int(parts[0]),
            minutes=int(parts[1]),
            seconds=int(parts[2]),
            milliseconds=int(parts[3])
        )

    def __repr__(self):
        return f"Subtitle({self.index}, {self.start_time} --> {self.end_time}, '{self.content[:20]}...')"


def parse_srt(file_path: str) -> list[SubtitleEntry]:
    """
    解析 SRT 文件。

    :param file_path: SRT 文件的路径。
    :return: 一个包含 SubtitleEntry 对象的列表。
    """
    logging.info(f"开始解析 SRT 文件: {file_path}")
    entries = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        # 使用正则表达式匹配每个字幕块
        pattern = re.compile(r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n([\s\S]+?)(?=\n\n|\Z)', re.MULTILINE)
        
        for match in pattern.finditer(content):
            index, start, end, text = match.groups()
            # 将一个条目中的所有行合并成一个完整的句子。
            # 只取第一行作为配音内容，以兼容单语和我们生成的双语字幕格式
            first_line = text.strip().split('\n')[0].strip()
            if first_line:
                entry = SubtitleEntry(index, start, end, first_line)
                entries.append(entry)

    except FileNotFoundError:
        logging.error(f"SRT 文件未找到: {file_path}")
        return []
    except Exception as e:
        logging.error(f"解析 SRT 文件时发生错误: {e}")
        return []
        
    logging.info(f"成功解析 {len(entries)} 条字幕。")
    return entries