# -*- coding: utf-8 -*-

import os
import logging
import tempfile
import shutil
from typing import List
from datetime import datetime

import srt_parser
import api_services
import video_processor
from srt_parser import SubtitleEntry
import ffmpeg

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_silent_audio(duration_seconds: float, output_path: str):
    """创建一个指定时长的静音音轨"""
    try:
        ffmpeg.input('anullsrc', format='lavfi', t=duration_seconds).output(output_path, acodec='pcm_s16le', ar='16000').run(overwrite_output=True, capture_stderr=True)
        logging.info(f"成功创建时长为 {duration_seconds}s 的静音音轨: {output_path}")
    except ffmpeg.Error as e:
        logging.error(f"创建静音音轨失败: {e.stderr.decode('utf8')}")
        raise

def get_video_duration(video_path: str) -> float:
    """获取视频时长（秒）"""
    try:
        probe = ffmpeg.probe(video_path)
        return float(probe['format']['duration'])
    except ffmpeg.Error as e:
        logging.error(f"获取视频时长失败: {e.stderr.decode('utf8')}")
        raise

def get_audio_duration(audio_path: str) -> float:
    """获取音频时长（秒）"""
    try:
        probe = ffmpeg.probe(audio_path)
        return float(probe['format']['duration'])
    except ffmpeg.Error as e:
        logging.error(f"获取音频 '{audio_path}' 时长失败: {e.stderr.decode('utf8')}")
        return 0.0 # 返回0.0以允许流程继续

def process_video_with_srt(
    video_path: str,
    srt_path: str,
    from_lang: str,
    to_lang: str,
    output_path: str,
    speaker: str,
    volume: float,
    provider: str
):
    """
    使用 SRT 文件为视频生成和替换配音的完整流程。
    """
    logging.info("===== 开始视频处理流程 =====")
    
    # 在项目根目录下创建带时间戳的临时文件夹
    base_temp_dir = os.path.join(os.getcwd(), "temp_output")
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    temp_dir = os.path.join(base_temp_dir, f"dubbing_{timestamp}")
    os.makedirs(temp_dir, exist_ok=True)
    logging.info(f"创建并确认临时目录: {temp_dir}")

    try:
        # 1. 提取原始音频并分离背景音
        logging.info("--- 步骤 1/4: 提取并分离音频 ---")
        original_audio_path = os.path.join(temp_dir, "original_audio.wav")
        if not video_processor.extract_audio(video_path, original_audio_path):
            raise RuntimeError("从视频中提取音频失败。")
        
        _, accompaniment_path = video_processor.separate_vocals(original_audio_path, temp_dir)
        if not accompaniment_path:
            logging.warning("无法分离背景音，将使用无背景音的配音。")
            # 创建一个与视频等长的静音文件作为背景音
            accompaniment_path = os.path.join(temp_dir, "silent_accompaniment.wav")
            create_silent_audio(get_video_duration(video_path), accompaniment_path)

        # 2. 生成配音音轨
        logging.info("--- 步骤 2/4: 生成配音音轨 ---")
        subtitles = srt_parser.parse_srt(srt_path)
        if not subtitles:
            raise ValueError("SRT 文件解析失败或内容为空。")

        dub_clips = []
        next_available_time = 0.0
        for sub in subtitles:
            translated_text = sub.content
            if from_lang != to_lang:
                translated_text = api_services.translate_text(sub.content, to_lang=to_lang, provider=provider)
            
            clip_path = os.path.join(temp_dir, f"clip_{sub.index}.mp3")
            success = api_services.text_to_speech_volcano(translated_text, lang=to_lang, speaker=speaker, output_path=clip_path)
            
            if success:
                # 标准化每个配音片段的响度
                normalized_clip_path = os.path.join(temp_dir, f"norm_clip_{sub.index}.mp3")
                if not video_processor.normalize_audio(clip_path, normalized_clip_path):
                    logging.warning(f"片段 {clip_path} 标准化失败，将使用原始片段。")
                    normalized_clip_path = clip_path # 如果失败则使用原始片段

                clip_duration = get_audio_duration(normalized_clip_path)
                original_start_time = sub.start_time.total_seconds()
                actual_start_time = max(original_start_time, next_available_time)
                next_available_time = actual_start_time + clip_duration
                dub_clips.append({"path": normalized_clip_path, "start_time": actual_start_time})
        
        if not dub_clips:
            raise ValueError("所有语音片段合成均失败。")
        
        # 将配音片段合成为一条音轨
        dub_duration = next_available_time
        base_dub_audio = os.path.join(temp_dir, "base_dub_audio.wav")
        create_silent_audio(dub_duration, base_dub_audio)
        
        dub_input_streams = [ffmpeg.input(base_dub_audio).audio] + [ffmpeg.input(c['path']).audio for c in dub_clips]
        dub_delayed_streams = [s.filter('adelay', f'{int(c["start_time"] * 1000)}|{int(c["start_time"] * 1000)}') for s, c in zip(dub_input_streams[1:], dub_clips)]
        
        # 使用 normalize=False 来防止 amix 自动降低音量，因为片段已经标准化且不重叠
        mixed_dub_audio = ffmpeg.filter([dub_input_streams[0]] + dub_delayed_streams, 'amix', inputs=len(dub_input_streams), duration='first', normalize=False)
        dub_audio_path = os.path.join(temp_dir, "dub_only_audio.mp3")
        try:
            ffmpeg.run(ffmpeg.output(mixed_dub_audio, dub_audio_path, acodec='mp3'), overwrite_output=True, capture_stderr=True)
            logging.info(f"配音音轨已合成: {dub_audio_path}")
        except ffmpeg.Error as e:
            logging.error(f"合成配音音轨失败，FFmpeg错误详情: {e.stderr.decode('utf8')}")
            raise

        # 3. 混合配音和背景音
        logging.info("--- 步骤 3/4: 混合新配音与原背景音 ---")
        final_audio_path = os.path.join(temp_dir, "final_mixed_audio.wav")

        try:
            # 获取配音音轨和背景音的时长
            dub_duration = get_audio_duration(dub_audio_path)
            accompaniment_duration = get_audio_duration(accompaniment_path)

            logging.info(f"配音音轨时长: {dub_duration:.2f}秒")
            logging.info(f"背景音时长: {accompaniment_duration:.2f}秒")

            input_dub = ffmpeg.input(dub_audio_path).audio

            # 如果背景音比配音短，需要延长背景音；如果背景音比配音长，需要截取背景音
            if accompaniment_duration < dub_duration:
                # 背景音比配音短，使用 apad 延长背景音
                logging.info(f"背景音比配音短 {dub_duration - accompaniment_duration:.2f}秒，将延长背景音")
                input_accompaniment = ffmpeg.input(accompaniment_path).audio.filter('apad', pad_dur=dub_duration - accompaniment_duration).filter('volume', '0.5')
            else:
                # 背景音比配音长或相等，截取背景音到配音长度
                logging.info(f"截取背景音到配音长度: {dub_duration:.2f}秒")
                input_accompaniment = ffmpeg.input(accompaniment_path).audio.filter('atrim', duration=dub_duration).filter('volume', '0.5')

            # 使用 amerge + pan 方案进行混合
            merged_stream = ffmpeg.filter([input_dub, input_accompaniment], 'amerge', inputs=2)
            mixed_stream = ffmpeg.filter(merged_stream, 'pan', 'stereo|c0<c0+c2|c1<c1+c3')

            # 在混合后，应用GUI滑块设定的总音量
            final_mixed_audio = mixed_stream.filter('volume', str(volume))

            ffmpeg.run(ffmpeg.output(final_mixed_audio, final_audio_path, acodec='pcm_s16le'), overwrite_output=True, capture_stderr=True)
            logging.info(f"最终混合音轨已合成: {final_audio_path}")
        except ffmpeg.Error as e:
            logging.error(f"混合音轨时发生错误，FFmpeg错误详情: {e.stderr.decode('utf8')}")
            raise

        # 4. 替换视频音轨并保证时长
        logging.info("--- 步骤 4/4: 替换视频音轨 ---")
        
        # 为最终音轨末尾添加3秒静音缓冲
        final_audio_buffered_path = os.path.join(temp_dir, "final_audio_buffered.wav")
        three_second_silence_path = os.path.join(temp_dir, "silence_3s.wav")
        create_silent_audio(3.0, three_second_silence_path)

        try:
            # 使用 concat 滤镜安全地连接音频
            input_main_audio = ffmpeg.input(final_audio_path)
            input_silence = ffmpeg.input(three_second_silence_path)
            concatenated_audio = ffmpeg.concat(input_main_audio, input_silence, v=0, a=1)
            ffmpeg.run(concatenated_audio.output(final_audio_buffered_path), overwrite_output=True, capture_stderr=True)
            logging.info(f"已为最终音轨添加3秒缓冲: {final_audio_buffered_path}")
            audio_to_use = final_audio_buffered_path
        except ffmpeg.Error as e:
            logging.error(f"添加音频缓冲失败，将使用原始音频: {e.stderr.decode('utf8')}")
            audio_to_use = final_audio_path # 如果失败，则继续使用原始的未缓冲音频

        success = video_processor.replace_audio(video_path, audio_to_use, output_path)
        if not success:
            raise RuntimeError("替换视频音轨失败。")

        logging.info(f"===== 视频处理流程成功完成！最终文件: {output_path} =====")

    except Exception as e:
        logging.error(f"处理流程中发生严重错误: {e}", exc_info=True)
    finally:
        logging.info(f"处理流程结束。临时文件保留在: {temp_dir}")
        # shutil.rmtree(temp_dir) # 根据用户要求，不再删除临时文件
import srt_writer

def generate_srt_from_video(
    video_path: str,
    from_lang: str,
    to_lang: str,
    output_srt_path: str,
    provider: str
):
    """
    从视频文件自动生成双语SRT字幕的完整流程。

    :param video_path: 输入视频文件的路径。
    :param from_lang: 源语言代码 (例如 'en')。
    :param to_lang: 目标语言代码 (例如 'zh')。
    :param output_srt_path: 输出SRT文件的保存路径。
    """
    logging.info("===== 开始从视频生成SRT字幕流程 =====")
    
    # 在项目根目录下创建带时间戳的临时文件夹
    base_temp_dir = os.path.join(os.getcwd(), "temp_output")
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    temp_dir = os.path.join(base_temp_dir, f"srt_generation_{timestamp}")
    os.makedirs(temp_dir, exist_ok=True)
    logging.info(f"创建并确认临时目录: {temp_dir}")

    try:
        # 1. 提取完整音轨
        full_audio_path = os.path.join(temp_dir, "full_audio.wav")
        if not video_processor.extract_audio(video_path, full_audio_path):
            raise RuntimeError("从视频中提取音频失败。")

        # 2. 人声分离
        vocals_path, _ = video_processor.separate_vocals(full_audio_path, temp_dir)
        if not vocals_path:
            raise RuntimeError("人声分离失败。")

        # 3. 音频切片 (只对人声进行切片)
        chunks_output_dir = os.path.join(temp_dir, "audio_chunks")
        audio_chunks = video_processor.split_audio_on_silence(vocals_path, chunks_output_dir)
        if not audio_chunks:
            raise ValueError("音频切片失败或未在音频中找到有效语音。")

        # 4. 循环处理音频片段：识别和翻译
        subtitle_entries = []
        for i, chunk in enumerate(audio_chunks):
            logging.info(f"--- 正在处理片段 {i+1}/{len(audio_chunks)} ---")
            
            # 语音识别
            asr_result = api_services.speech_to_text(chunk['path'])
            if not asr_result or not asr_result.get("result"):
                logging.warning(f"片段 {chunk['path']} 语音识别失败，跳过。")
                continue
            
            source_text = asr_result["result"][0]
            
            # 文本翻译
            translated_text = api_services.translate_text(source_text, to_lang=to_lang, provider=provider)
            if translated_text == source_text:
                logging.warning(f"文本 '{source_text}' 翻译失败或结果相同。")
            
            subtitle_entries.append({
                "start_ms": chunk['start_ms'],
                "end_ms": chunk['end_ms'],
                "source_text": source_text,
                "target_text": translated_text
            })

        if not subtitle_entries:
            raise ValueError("所有音频片段处理均失败，未能生成任何字幕条目。")

        # 5. 写入SRT文件
        if not srt_writer.write_srt(subtitle_entries, output_srt_path):
            raise RuntimeError("写入最终的SRT文件失败。")
            
        logging.info(f"===== SRT字幕生成流程成功完成！最终文件: {output_srt_path} =====")

    except Exception as e:
        logging.error(f"生成SRT字幕流程中发生严重错误: {e}", exc_info=True)
    finally:
        logging.info(f"处理流程结束。临时文件保留在: {temp_dir}")
        # shutil.rmtree(temp_dir) # 根据用户要求，不再删除临时文件