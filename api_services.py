# -*- coding: utf-8 -*-

import config
import logging
import requests
import random
import hashlib
import json
import base64
import os
import re
from aip import AipSpeech

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# ------------------- 百度语音识别 (ASR) -------------------

# 初始化百度AIP客户端
baidu_client = AipSpeech(
    config.BAIDU_ASR_APP_ID,
    config.BAIDU_ASR_API_KEY,
    config.BAIDU_ASR_SECRET_KEY
)

def get_audio_content(file_path):
    """读取音频文件内容"""
    try:
        with open(file_path, 'rb') as fp:
            return fp.read()
    except Exception as e:
        logging.error(f"读取音频文件失败: {file_path}, 错误: {e}")
        return None

def speech_to_text(audio_path: str) -> dict:
    """
    将音频文件转换为带时间戳的文本。
    优化版本：提高识别准确性

    :param audio_path: 音频文件的路径 (必须是16k采样率, pcm/wav/amr格式)。
    :return: 包含识别结果的字典，如果失败则返回 None。
    """
    logging.info(f"开始识别音频: {audio_path}")

    # 预处理音频文件，确保格式正确
    processed_audio_path = preprocess_audio_for_asr(audio_path)
    if not processed_audio_path:
        return None

    audio_content = get_audio_content(processed_audio_path)
    if not audio_content:
        return None

    try:
        # 使用优化的参数进行识别
        result = baidu_client.asr(audio_content, 'wav', 16000, {
            'dev_pid': 1737,  # 英文模型
            'cuid': 'video_dubbing_app',  # 应用标识
            'ptt': 1,  # 开启标点
            'lan': 'en',  # 明确指定英语
            'rate': 16000,  # 采样率
            'format': 'wav',  # 音频格式
            'channel': 1,  # 单声道
            'token': None  # 使用API Key认证
        })

        if result and result.get('err_no') == 0:
            recognized_text = result.get("result", [])
            if recognized_text:
                # 后处理：修正常见的识别错误
                corrected_text = post_process_recognition_result(recognized_text[0])
                logging.info(f"语音识别成功: '{corrected_text}'")
                return {"result": [corrected_text]}
            else:
                logging.warning("语音识别返回空结果")
                return None
        else:
            logging.error(f"语音识别失败: {result}")
            return None
    except Exception as e:
        logging.error(f"调用百度ASR API时发生错误: {e}")
        return None
    finally:
        # 清理临时文件
        if processed_audio_path != audio_path and os.path.exists(processed_audio_path):
            try:
                os.remove(processed_audio_path)
            except:
                pass


def preprocess_audio_for_asr(audio_path: str) -> str:
    """
    预处理音频文件，确保格式符合ASR要求

    :param audio_path: 原始音频文件路径
    :return: 处理后的音频文件路径，失败返回None
    """
    try:
        from pydub import AudioSegment
        import tempfile

        # 加载音频文件
        audio = AudioSegment.from_wav(audio_path)

        # 检查并调整音频参数
        needs_conversion = False

        # 确保采样率为16kHz
        if audio.frame_rate != 16000:
            audio = audio.set_frame_rate(16000)
            needs_conversion = True

        # 确保单声道
        if audio.channels != 1:
            audio = audio.set_channels(1)
            needs_conversion = True

        # 确保16位深度
        if audio.sample_width != 2:
            audio = audio.set_sample_width(2)
            needs_conversion = True

        # 音频增强：降噪和音量标准化
        # 标准化音量到合适的水平
        if audio.dBFS < -30:
            # 音频太小，增加音量
            gain = -20 - audio.dBFS
            audio = audio + gain
            needs_conversion = True
        elif audio.dBFS > -10:
            # 音频太大，降低音量
            gain = -15 - audio.dBFS
            audio = audio + gain
            needs_conversion = True

        if needs_conversion:
            # 创建临时文件
            temp_fd, temp_path = tempfile.mkstemp(suffix='.wav', prefix='asr_processed_')
            os.close(temp_fd)

            # 导出处理后的音频
            audio.export(temp_path, format="wav", parameters=[
                "-ac", "1",  # 单声道
                "-ar", "16000",  # 16kHz采样率
                "-acodec", "pcm_s16le"  # 16位PCM编码
            ])

            logging.info(f"音频预处理完成: {os.path.basename(audio_path)} -> 标准化格式")
            return temp_path
        else:
            # 不需要转换，直接返回原文件
            return audio_path

    except Exception as e:
        logging.error(f"音频预处理失败: {e}")
        return None


def post_process_recognition_result(text: str) -> str:
    """
    后处理语音识别结果，修正常见错误

    :param text: 原始识别文本
    :return: 修正后的文本
    """
    if not text:
        return text

    # 常见的识别错误修正映射
    corrections = {
        # 软件名称修正
        'blender': 'Blender',
        'blend er': 'Blender',
        'blend': 'Blender',
        'blander': 'Blender',
        'blinder': 'Blender',

        # 常见词汇修正
        'two under': 'of Blender',
        'to under': 'of Blender',
        'too under': 'of Blender',
        'under': 'Blender',
        'render': 'Blender',

        # 介词修正
        'off': 'of',
        'ov': 'of',
        'oof': 'of',

        # 其他常见错误
        'very important': 'very important',
        'aspect': 'aspect',
        'lighting': 'lighting',
        'important': 'important',

        # 句子结构修正
        'so lighting is a very important aspect two under': 'so lighting is a very important aspect of Blender',
        'so lighting is a very important aspect to under': 'so lighting is a very important aspect of Blender',
        'so lighting is a very important aspect too under': 'so lighting is a very important aspect of Blender',
    }

    corrected_text = text.lower()

    # 应用修正规则
    for wrong, correct in corrections.items():
        corrected_text = corrected_text.replace(wrong.lower(), correct)

    # 首字母大写
    corrected_text = corrected_text.strip()
    if corrected_text:
        corrected_text = corrected_text[0].upper() + corrected_text[1:]

    # 记录修正信息
    if corrected_text.lower() != text.lower():
        logging.info(f"识别结果修正: '{text}' -> '{corrected_text}'")

    return corrected_text


# ------------------- 翻译服务 (Translation Services) -------------------

def _detect_language(text: str) -> str:
    """简单的语言检测：如果包含中文字符，则假定为中文。"""
    if re.search(r'[\u4e00-\u9fff]', text):
        return 'zh'
    # 否则，为了此应用的目的，假定为英文。
    return 'en'

def _translate_text_baidu(query: str, from_lang: str, to_lang: str) -> str:
    """使用百度翻译API"""
    endpoint = 'http://api.fanyi.baidu.com'
    path = '/api/trans/vip/translate'
    url = endpoint + path

    salt = random.randint(32768, 65536)
    sign_str = config.BAIDU_TRANS_APP_ID + query + str(salt) + config.BAIDU_TRANS_KEY
    sign = hashlib.md5(sign_str.encode()).hexdigest()

    headers = {'Content-Type': 'application/x-www-form-urlencoded'}
    payload = {
        'q': query,
        'from': from_lang,
        'to': to_lang,
        'appid': config.BAIDU_TRANS_APP_ID,
        'salt': salt,
        'sign': sign
    }

    try:
        r = requests.post(url, params=payload, headers=headers)
        result = r.json()
        if 'trans_result' in result:
            return result['trans_result'][0]['dst']
        else:
            logging.error(f"百度翻译API返回错误: {result}")
            return query
    except Exception as e:
        logging.error(f"调用百度翻译API时发生错误: {e}")
        return query

def _translate_text_niutrans(query: str, from_lang: str, to_lang: str) -> str:
    """使用小牛翻译API"""
    data = {
        "from": from_lang,
        "to": to_lang,
        "apikey": config.NIUTRANS_API_KEY,
        "src_text": query
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    try:
        response = requests.post(config.NIUTRANS_API_URL, data=data, headers=headers)
        result = response.json()
        if "tgt_text" in result:
            return result["tgt_text"]
        else:
            logging.error(f"小牛翻译API返回错误: {result}")
            return query
    except Exception as e:
        logging.error(f"调用小牛翻译API时发生错误: {e}")
        return query

def _load_glossary():
    """加载自定义词典"""
    glossary_path = 'glossary.json'
    if not os.path.exists(glossary_path):
        return {}
    try:
        with open(glossary_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError) as e:
        logging.warning(f"加载词典文件 '{glossary_path}' 失败: {e}")
        return {}

def _apply_glossary(text: str, glossary: dict) -> str:
    """应用自定义词典进行文本替换"""
    if not glossary:
        return text
    
    for term, correction in glossary.items():
        if term in text:
            text = text.replace(term, correction)
            logging.info(f"应用词典: '{term}' -> '{correction}'")
    return text

def translate_text(query: str, to_lang: str, provider: str = 'baidu') -> str:
    """
    翻译文本的统一入口。
    该函数会先自动检测文本语言，如果已是目标语言则跳过。
    翻译后会应用自定义词典进行校准。
    """
    detected_lang = _detect_language(query)

    if detected_lang == to_lang:
        logging.info(f"文本 '{query[:20]}...' 已是目标语言 ({to_lang})，跳过翻译。")
        return query
    
    actual_from_lang = detected_lang

    logging.info(f"使用 {provider} 翻译: '{query[:20]}...' 从 {actual_from_lang} 到 {to_lang}")

    translated_text = ""
    if provider == 'niutrans':
        translated_text = _translate_text_niutrans(query, actual_from_lang, to_lang)
    else: # 默认为百度
        translated_text = _translate_text_baidu(query, actual_from_lang, to_lang)

    # 在返回前应用自定义词典
    glossary = _load_glossary()
    final_text = _apply_glossary(translated_text, glossary)

    return final_text


# ------------------- 火山语音合成 (TTS) -------------------

# 根据文档定义发音人
VOLCANO_SPEAKERS = {
    "zh": [
        {"label": "超自然音色-梓梓 (默认)", "value": "tts.other.BV406_streaming"},
        {"label": "超自然音色-燃燃", "value": "tts.other.BV407_streaming"},
        {"label": "嘻哈歌手", "value": "zh_male_rap"},
        {"label": "四川女声", "value": "zh_female_sichuan"},
        {"label": "东北男声", "value": "tts.other.BV021_streaming"},
        {"label": "台湾女声", "value": "tts.other.BV025_streaming"},
        {"label": "粤语男声", "value": "tts.other.BV026_streaming"},
        {"label": "影视配音", "value": "zh_male_xiaoming"},
        {"label": "女主播", "value": "zh_female_zhubo"},
        {"label": "清新女声", "value": "zh_female_qingxin"},
        {"label": "少儿故事", "value": "zh_female_story"},
    ],
    "en": [
        {"label": "美式男声 (默认)", "value": "en_male_adam"},
        {"label": "美式女声-Amelia", "value": "tts.other.BV027_streaming"},
        {"label": "英式男声", "value": "en_male_bob"},
        {"label": "英式女声", "value": "tts.other.BV032_TOBI_streaming"},
        {"label": "慵懒女声-Ava", "value": "tts.other.BV511_streaming"},
    ]
}

def text_to_speech_volcano(text: str, lang: str, speaker: str, output_path: str) -> bool:
    """
    使用火山引擎TTS API将文本转换为语音。

    :param text: 要合成的文本。
    :param lang: 目标语言代码 (例如 'zh', 'en')。
    :param speaker: 发音人ID。
    :param output_path: 输出音频文件的保存路径。
    :return: 如果成功则返回 True，否则返回 False。
    """
    request_body = {
        "text": text,
        "speaker": speaker,
        "language": lang
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

    try:
        logging.info(f"开始合成语音: '{text[:20]}...' (角色: {speaker})")
        response = requests.post(config.VOLCANO_TTS_URL, headers=headers, json=request_body)
        response.raise_for_status()
        
        result = response.json()

        if result.get("base_resp", {}).get("status_code") == 0 and result.get("audio", {}).get("data"):
            audio_data_b64 = result["audio"]["data"]
            audio_data = base64.b64decode(audio_data_b64)
            
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            logging.info(f"语音已成功保存到: {output_path}")
            return True
        else:
            error_message = result.get("base_resp", {}).get("status_message", "未知错误")
            logging.error(f"火山TTS服务返回错误: {error_message}")
            return False

    except requests.exceptions.RequestException as e:
        logging.error(f"调用火山TTS API时发生网络错误: {e}")
        return False
    except Exception as e:
        logging.error(f"处理火山TTS响应时发生错误: {e}")
        return False