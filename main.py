# -*- coding: utf-8 -*-

import sys
import os
from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QFileDialog, QLabel,
    QComboBox, QProgressBar, QSlider, QGridLayout
)
from PySide6.QtCore import QThread, Signal, Qt

import main_controller
import api_services # 需要导入来获取发音人列表

# 工作线程
class Worker(QThread):
    progress = Signal(int)
    status = Signal(str)
    finished = Signal(bool, str)

    def __init__(self, video_path, srt_path, from_lang, to_lang, output_path, speaker, volume, provider):
        super().__init__()
        self.video_path = video_path
        self.srt_path = srt_path
        self.from_lang = from_lang
        self.to_lang = to_lang
        self.output_path = output_path
        self.speaker = speaker
        self.volume = volume
        self.provider = provider

    def run(self):
        try:
            self.status.emit("开始处理...")
            self.progress.emit(10)
            
            main_controller.process_video_with_srt(
                self.video_path, self.srt_path, self.from_lang, self.to_lang,
                self.output_path, self.speaker, self.volume, self.provider
            )
            
            self.progress.emit(100)
            self.status.emit("处理完成！")
            self.finished.emit(True, f"成功！文件已保存到: {self.output_path}")

        except Exception as e:
            self.status.emit(f"错误: {e}")
            self.finished.emit(False, f"处理失败: {e}")


# 新增：字幕生成工作线程
class SrtGenerationWorker(QThread):
    progress = Signal(int) # 暂时不用，但为了接口统一保留
    status = Signal(str)
    finished = Signal(bool, str)

    def __init__(self, video_path, from_lang, to_lang, output_srt_path, provider):
        super().__init__()
        self.video_path = video_path
        self.from_lang = from_lang
        self.to_lang = to_lang
        self.output_srt_path = output_srt_path
        self.provider = provider

    def run(self):
        try:
            self.status.emit("开始生成字幕，请耐心等待...")
            self.progress.emit(10) # 示意性进度

            main_controller.generate_srt_from_video(
                self.video_path,
                self.from_lang,
                self.to_lang,
                self.output_srt_path,
                self.provider
            )

            self.progress.emit(100)
            self.status.emit("字幕生成完成！")
            self.finished.emit(True, f"成功！字幕文件已保存到: {self.output_srt_path}")

        except Exception as e:
            self.status.emit(f"错误: {e}")
            self.finished.emit(False, f"字幕生成失败: {e}")


# 主窗口
class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频自动配音工具 v1.1")
        self.layout = QVBoxLayout(self)
        self.grid_layout = QGridLayout()

        # 文件选择
        self.video_label = QLabel("未选择视频文件")
        self.btn_select_video = QPushButton("1. 选择视频文件 (.mp4)")
        self.btn_select_video.clicked.connect(self.select_video)
        self.srt_label = QLabel("未选择字幕文件")
        self.btn_select_srt = QPushButton("2. 选择字幕文件 (.srt)")
        self.btn_select_srt.clicked.connect(self.select_srt)
        
        self.grid_layout.addWidget(self.btn_select_video, 0, 0)
        self.grid_layout.addWidget(self.video_label, 0, 1)
        self.grid_layout.addWidget(self.btn_select_srt, 1, 0)
        self.grid_layout.addWidget(self.srt_label, 1, 1)

        # 语言和角色选择
        self.from_lang_combo = QComboBox()
        self.from_lang_combo.addItems(["en", "zh"])
        self.to_lang_combo = QComboBox()
        self.to_lang_combo.addItems(["zh", "en"])
        self.speaker_combo = QComboBox()
        self.to_lang_combo.currentTextChanged.connect(self.update_speaker_choices)
        
        self.grid_layout.addWidget(QLabel("源语言:"), 2, 0)
        self.grid_layout.addWidget(self.from_lang_combo, 2, 1)
        self.grid_layout.addWidget(QLabel("目标语言:"), 3, 0)
        self.grid_layout.addWidget(self.to_lang_combo, 3, 1)
        self.grid_layout.addWidget(QLabel("配音角色:"), 4, 0)
        self.grid_layout.addWidget(self.speaker_combo, 4, 1)

        # 新增：翻译服务商选择
        self.provider_combo = QComboBox()
        self.provider_combo.addItems(["百度翻译", "小牛翻译"])
        self.grid_layout.addWidget(QLabel("翻译服务:"), 5, 0)
        self.grid_layout.addWidget(self.provider_combo, 5, 1)
        
        # 音量控制
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setMinimum(10) # 1.0x
        self.volume_slider.setMaximum(50) # 5.0x
        self.volume_slider.setValue(20)   # 默认 2.0x
        self.volume_label = QLabel(f"配音音量: {self.volume_slider.value()/10.0:.1f}x")
        self.volume_slider.valueChanged.connect(lambda v: self.volume_label.setText(f"配音音量: {v/10.0:.1f}x"))
        
        self.grid_layout.addWidget(QLabel("音量调节:"), 6, 0)
        self.grid_layout.addWidget(self.volume_slider, 6, 1)
        self.grid_layout.addWidget(self.volume_label, 7, 1)

        self.layout.addLayout(self.grid_layout)

        # 功能按钮
        self.buttons_layout = QHBoxLayout()
        self.btn_generate_srt = QPushButton("A. 从视频生成字幕")
        self.btn_generate_srt.clicked.connect(self.start_srt_generation)
        self.btn_start_dubbing = QPushButton("B. 为视频配音")
        self.btn_start_dubbing.clicked.connect(self.start_dubbing)
        
        self.buttons_layout.addWidget(self.btn_generate_srt)
        self.buttons_layout.addWidget(self.btn_start_dubbing)
        self.layout.addLayout(self.buttons_layout)

        # 状态显示
        self.progress_bar = QProgressBar()
        self.status_label = QLabel("提示: 功能A需要选择视频; 功能B需要选择视频和字幕。")
        
        self.layout.addWidget(self.progress_bar)
        self.layout.addWidget(self.status_label)

        self.video_path = ""
        self.srt_path = ""
        self.update_speaker_choices(self.to_lang_combo.currentText()) # 初始化
        self.dub_worker = None
        self.srt_worker = None

    def closeEvent(self, event):
        """确保在关闭窗口前，后台线程已结束"""
        if self.dub_worker and self.dub_worker.isRunning():
            self.dub_worker.quit()
            self.dub_worker.wait() # 等待线程结束
        if self.srt_worker and self.srt_worker.isRunning():
            self.srt_worker.quit()
            self.srt_worker.wait() # 等待线程结束
        event.accept()

    def update_speaker_choices(self, lang):
        self.speaker_combo.clear()
        speakers = api_services.VOLCANO_SPEAKERS.get(lang, [])
        for speaker in speakers:
            self.speaker_combo.addItem(speaker["label"], speaker["value"])

    def select_video(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择视频文件", "", "Video Files (*.mp4)")
        if path:
            self.video_path = path
            self.video_label.setText(f"视频: ...{os.path.basename(path)}")

    def select_srt(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择字幕文件", "", "SRT Files (*.srt)")
        if path:
            self.srt_path = path
            self.srt_label.setText(f"字幕: ...{os.path.basename(path)}")

    def start_srt_generation(self):
        if not self.video_path:
            self.status_label.setText("错误: 请先选择视频文件！")
            return

        from_lang = self.from_lang_combo.currentText()
        to_lang = self.to_lang_combo.currentText()
        provider = "niutrans" if self.provider_combo.currentText() == "小牛翻译" else "baidu"

        base, _ = os.path.splitext(self.video_path)
        output_srt_path, _ = QFileDialog.getSaveFileName(self, "保存字幕文件", f"{base}.srt", "SRT Files (*.srt)")

        if not output_srt_path:
            self.status_label.setText("操作已取消。")
            return

        self.btn_generate_srt.setEnabled(False)
        self.btn_start_dubbing.setEnabled(False)
        self.status_label.setText("正在准备生成字幕...")

        self.srt_worker = SrtGenerationWorker(
            self.video_path, from_lang, to_lang, output_srt_path, provider
        )
        self.srt_worker.progress.connect(self.progress_bar.setValue)
        self.srt_worker.status.connect(self.status_label.setText)
        self.srt_worker.finished.connect(self.on_finished)
        self.srt_worker.start()

    def start_dubbing(self):
        if not self.video_path or not self.srt_path:
            self.status_label.setText("错误: 请确保已选择视频和字幕文件！")
            return

        from_lang = self.from_lang_combo.currentText()
        to_lang = self.to_lang_combo.currentText()
        speaker = self.speaker_combo.currentData()
        volume = self.volume_slider.value() / 10.0
        provider = "niutrans" if self.provider_combo.currentText() == "小牛翻译" else "baidu"
        
        base, _ = os.path.splitext(self.video_path)
        output_path, _ = QFileDialog.getSaveFileName(self, "保存配音视频", f"{base}_dubbed.mp4", "Video Files (*.mp4)")
        
        if not output_path:
            self.status_label.setText("操作已取消。")
            return

        self.btn_generate_srt.setEnabled(False)
        self.btn_start_dubbing.setEnabled(False)
        self.status_label.setText("正在准备配音...")

        self.dub_worker = Worker(
            self.video_path, self.srt_path, from_lang, to_lang,
            output_path, speaker, volume, provider # 传递 provider
        )
        self.dub_worker.progress.connect(self.progress_bar.setValue)
        self.dub_worker.status.connect(self.status_label.setText)
        self.dub_worker.finished.connect(self.on_finished)
        self.dub_worker.start()

    def on_finished(self, success, message):
        self.status_label.setText(message)
        self.btn_generate_srt.setEnabled(True)
        self.btn_start_dubbing.setEnabled(True)

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.resize(500, 400)
    window.show()
    sys.exit(app.exec())

if __name__ == '__main__':
    main()