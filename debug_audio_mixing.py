#!/usr/bin/env python3
"""
调试音频混合问题的脚本
"""
import os
import ffmpeg
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_audio_mixing():
    """测试音频混合功能"""
    # 使用最近的临时目录
    temp_dir = "temp_output/dubbing_20250802-175244"
    
    if not os.path.exists(temp_dir):
        logging.error(f"临时目录不存在: {temp_dir}")
        return
    
    dub_audio_path = os.path.join(temp_dir, "dub_only_audio.mp3")
    accompaniment_path = os.path.join(temp_dir, "accompaniment.wav")
    
    if not os.path.exists(dub_audio_path):
        logging.error(f"配音文件不存在: {dub_audio_path}")
        return
        
    if not os.path.exists(accompaniment_path):
        logging.error(f"背景音文件不存在: {accompaniment_path}")
        return
    
    # 检查文件信息
    try:
        dub_probe = ffmpeg.probe(dub_audio_path)
        acc_probe = ffmpeg.probe(accompaniment_path)
        
        logging.info("=== 配音音轨信息 ===")
        dub_stream = dub_probe['streams'][0]
        logging.info(f"编解码器: {dub_stream.get('codec_name', 'unknown')}")
        logging.info(f"采样率: {dub_stream.get('sample_rate', 'unknown')}")
        logging.info(f"声道数: {dub_stream.get('channels', 'unknown')}")
        logging.info(f"时长: {dub_stream.get('duration', 'unknown')}")
        
        logging.info("=== 背景音轨信息 ===")
        acc_stream = acc_probe['streams'][0]
        logging.info(f"编解码器: {acc_stream.get('codec_name', 'unknown')}")
        logging.info(f"采样率: {acc_stream.get('sample_rate', 'unknown')}")
        logging.info(f"声道数: {acc_stream.get('channels', 'unknown')}")
        logging.info(f"时长: {acc_stream.get('duration', 'unknown')}")
        
    except Exception as e:
        logging.error(f"获取音频信息失败: {e}")
        return
    
    # 测试不同的混合方法
    test_methods = [
        ("amix_simple", test_amix_simple),
        ("amerge_pan", test_amerge_pan),
        ("amix_normalized", test_amix_normalized)
    ]
    
    for method_name, method_func in test_methods:
        logging.info(f"\n=== 测试方法: {method_name} ===")
        try:
            output_path = os.path.join(temp_dir, f"test_{method_name}.mp3")
            method_func(dub_audio_path, accompaniment_path, output_path)
            logging.info(f"✓ {method_name} 成功")
        except ffmpeg.Error as e:
            logging.error(f"✗ {method_name} 失败，FFmpeg错误详情:")
            logging.error(e.stderr.decode('utf8'))
        except Exception as e:
            logging.error(f"✗ {method_name} 失败: {e}")

def test_amix_simple(dub_path, acc_path, output_path):
    """测试简单的amix混合"""
    input_dub = ffmpeg.input(dub_path).audio
    input_acc = ffmpeg.input(acc_path).audio.filter('volume', '0.5')
    mixed = ffmpeg.filter([input_dub, input_acc], 'amix', inputs=2, duration='first')
    ffmpeg.run(ffmpeg.output(mixed, output_path, acodec='aac', audio_bitrate='192k'), 
               overwrite_output=True, capture_stderr=True)

def test_amerge_pan(dub_path, acc_path, output_path):
    """测试amerge+pan混合"""
    input_dub = ffmpeg.input(dub_path).audio
    input_acc = ffmpeg.input(acc_path).audio.filter('volume', '0.5')
    merged = ffmpeg.filter([input_dub, input_acc], 'amerge', inputs=2)
    panned = ffmpeg.filter(merged, 'pan', 'stereo|c0<c0+c2|c1<c1+c3')
    ffmpeg.run(ffmpeg.output(panned, output_path, acodec='aac', audio_bitrate='192k'), 
               overwrite_output=True, capture_stderr=True)

def test_amix_normalized(dub_path, acc_path, output_path):
    """测试标准化后的amix混合"""
    # 先将两个音频标准化为相同格式
    input_dub = ffmpeg.input(dub_path).audio.filter('aformat', sample_fmts='fltp', sample_rates='44100', channel_layouts='stereo')
    input_acc = ffmpeg.input(acc_path).audio.filter('aformat', sample_fmts='fltp', sample_rates='44100', channel_layouts='stereo').filter('volume', '0.5')
    mixed = ffmpeg.filter([input_dub, input_acc], 'amix', inputs=2, duration='first')
    # 使用WAV格式输出避免MP3编码问题
    ffmpeg.run(ffmpeg.output(mixed, output_path.replace('.mp3', '.wav'), acodec='pcm_s16le'),
               overwrite_output=True, capture_stderr=True)

if __name__ == "__main__":
    test_audio_mixing()
