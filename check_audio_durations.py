#!/usr/bin/env python3
"""
检查音频文件时长，诊断final_audio_buffered.wav的问题
"""
import os
import ffmpeg
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_audio_durations():
    """检查各个音频文件的时长"""
    
    # 使用最新的临时目录
    temp_dir = "temp_output/dubbing_20250802-202206"
    
    if not os.path.exists(temp_dir):
        logging.error(f"临时目录不存在: {temp_dir}")
        return
    
    # 要检查的音频文件
    audio_files = [
        "dub_only_audio.mp3",
        "final_mixed_audio.wav", 
        "final_audio_buffered.wav",
        "accompaniment.wav"
    ]
    
    logging.info("=== 音频文件时长检查 ===")
    
    for filename in audio_files:
        file_path = os.path.join(temp_dir, filename)
        
        if os.path.exists(file_path):
            try:
                probe = ffmpeg.probe(file_path)
                duration = float(probe['format']['duration'])
                stream = probe['streams'][0]
                
                logging.info(f"\n📁 {filename}:")
                logging.info(f"  时长: {duration:.2f}秒")
                logging.info(f"  编解码器: {stream.get('codec_name', 'unknown')}")
                logging.info(f"  采样率: {stream.get('sample_rate', 'unknown')}")
                logging.info(f"  声道数: {stream.get('channels', 'unknown')}")
                
            except Exception as e:
                logging.error(f"  ❌ 无法获取 {filename} 的信息: {e}")
        else:
            logging.warning(f"  ⚠️  文件不存在: {filename}")
    
    # 检查原视频时长作为参考
    video_path = "01. Installing and Opening Blender for the First Time Mammoth.mp4"
    if os.path.exists(video_path):
        try:
            probe = ffmpeg.probe(video_path)
            duration = float(probe['format']['duration'])
            logging.info(f"\n📹 原视频时长: {duration:.2f}秒")
        except Exception as e:
            logging.error(f"无法获取视频时长: {e}")

if __name__ == "__main__":
    check_audio_durations()
