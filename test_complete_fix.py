#!/usr/bin/env python3
"""
完整测试修复后的视频配音流程
"""
import os
import ffmpeg
import logging
from video_processor import replace_audio
from main_controller import get_audio_duration

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_complete_fix():
    """测试完整的修复流程"""
    
    # 使用修复后生成的音频文件
    temp_dir = "temp_output/dubbing_20250802-202206"
    video_path = "01. Installing and Opening Blender for the First Time Mammoth.mp4"
    
    # 使用修复后的音频文件
    fixed_audio_path = os.path.join(temp_dir, "test_final_audio_buffered_fixed.wav")
    output_video_path = "test_complete_fix_output.mp4"
    
    if not os.path.exists(video_path):
        logging.error(f"视频文件不存在: {video_path}")
        return False
        
    if not os.path.exists(fixed_audio_path):
        logging.error(f"修复后的音频文件不存在: {fixed_audio_path}")
        return False
    
    logging.info("开始完整测试修复后的视频配音流程...")
    logging.info(f"输入视频: {video_path}")
    logging.info(f"修复后的音频: {fixed_audio_path}")
    logging.info(f"输出视频: {output_video_path}")
    
    try:
        # 获取时长信息
        video_probe = ffmpeg.probe(video_path)
        audio_probe = ffmpeg.probe(fixed_audio_path)
        
        video_duration = float(video_probe['format']['duration'])
        audio_duration = float(audio_probe['format']['duration'])
        
        logging.info(f"原视频时长: {video_duration:.2f}秒")
        logging.info(f"修复后音频时长: {audio_duration:.2f}秒")
        logging.info(f"时长差异: {audio_duration - video_duration:.2f}秒")
        
        # 使用修复后的replace_audio函数
        success = replace_audio(video_path, fixed_audio_path, output_video_path)
        
        if success and os.path.exists(output_video_path):
            # 检查输出视频
            output_probe = ffmpeg.probe(output_video_path)
            output_duration = float(output_probe['format']['duration'])
            
            logging.info(f"✓ 视频处理成功！")
            logging.info(f"输出视频时长: {output_duration:.2f}秒")
            logging.info(f"音频时长: {audio_duration:.2f}秒")
            
            # 验证输出视频时长是否足够容纳完整音频
            if output_duration >= audio_duration - 0.1:  # 允许0.1秒的误差
                logging.info("✓ 完整修复成功！输出视频能够完整播放配音")
                
                # 额外验证：检查音频是否真的完整
                dub_only_path = os.path.join(temp_dir, "dub_only_audio.mp3")
                if os.path.exists(dub_only_path):
                    dub_only_duration = get_audio_duration(dub_only_path)
                    logging.info(f"原始配音时长: {dub_only_duration:.2f}秒")
                    
                    if output_duration >= dub_only_duration + 2.5:  # 至少包含原始配音 + 2.5秒缓冲
                        logging.info("✓ 验证通过：输出视频包含完整配音和足够缓冲")
                        return True
                    else:
                        logging.error(f"✗ 验证失败：输出视频时长不足以包含完整配音")
                        return False
                else:
                    logging.warning("无法找到原始配音文件进行验证，但基本测试通过")
                    return True
            else:
                logging.error(f"✗ 修复失败！输出视频时长不足，缺少 {audio_duration - output_duration:.2f}秒")
                return False
        else:
            logging.error("✗ 视频处理失败")
            return False
            
    except Exception as e:
        logging.error(f"✗ 完整测试失败，发生异常: {e}")
        return False

if __name__ == "__main__":
    success = test_complete_fix()
    if success:
        print("\n🎉 完整修复测试成功！配音文件现在可以完整播放了！")
    else:
        print("\n❌ 完整修复测试失败")
