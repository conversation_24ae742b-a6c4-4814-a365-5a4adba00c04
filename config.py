'''
Author: mobolun <EMAIL>
Date: 2025-07-25 01:14:48
LastEditors: mobolun <EMAIL>
LastEditTime: 2025-07-25 01:15:05
FilePath: \VedioLocal\config.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
# -*- coding: utf-8 -*-

"""
配置信息
"""

# 百度语音识别
BAIDU_ASR_APP_ID = '你的 App ID'  # 请替换为实际的 APP ID
BAIDU_ASR_API_KEY = 'o0Xm2NeRCvM2FPu3X08kllF1'
BAIDU_ASR_SECRET_KEY = '6N7CT0iVv4bKw6eNPDN53yBatoC1yeQR'

# 百度翻译
BAIDU_TRANS_APP_ID = '20201016000591089'
BAIDU_TRANS_KEY = 'MZy84qVDEhthizpYwfWj'

# 火山语音合成 (无需Key)
VOLCANO_TTS_URL = "https://translate.volcengine.com/crx/tts/v1/"
# 小牛翻译
NIUTRANS_API_KEY = '272b7e507be4b393ce8d7271b68f6a3f'
NIUTRANS_API_URL = 'https://api.niutrans.com/NiuTransServer/translation'