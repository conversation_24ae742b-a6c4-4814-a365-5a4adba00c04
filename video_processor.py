'''
Author: mobolun <EMAIL>
Date: 2025-07-25 01:15:30
LastEditors: mobolun <EMAIL>
LastEditTime: 2025-07-26 16:41:37
FilePath: \VedioLocal\video_processor.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
# -*- coding: utf-8 -*-

import ffmpeg
import logging
import os

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def extract_audio(video_path: str, output_audio_path: str) -> bool:
    """
    从视频文件中提取音频。

    :param video_path: 输入视频文件的路径。
    :param output_audio_path: 输出音频文件的保存路径。
    :return: 如果成功则返回 True，否则返回 False。
    """
    logging.info(f"开始从 '{video_path}' 提取音频...")
    try:
        if not os.path.exists(video_path):
            logging.error(f"视频文件不存在: {video_path}")
            return False
            
        (
            ffmpeg
            .input(video_path)
            .output(
                output_audio_path,
                acodec='pcm_s16le',  # 百度ASR推荐的编码
                ac=1,                # 单声道
                ar='16000'           # 16k 采样率
            )
            .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
        )
        logging.info(f"音频已成功提取到: '{output_audio_path}'")
        return True
    except ffmpeg.Error as e:
        logging.error("提取音频时发生 ffmpeg 错误:")
        logging.error(e.stderr.decode('utf8'))
        return False
    except Exception as e:
        logging.error(f"提取音频时发生未知错误: {e}")
        return False

def replace_audio(video_path: str, new_audio_path: str, output_video_path: str) -> bool:
    """
    用新的音轨替换视频中的原始音轨，确保视频长度能够容纳完整的音频播放。

    :param video_path: 原始视频文件的路径。
    :param new_audio_path: 新音频文件的路径。
    :param output_video_path: 输出视频文件的保存路径。
    :return: 如果成功则返回 True，否则返回 False。
    """
    logging.info(f"开始将 '{new_audio_path}' 的音频合并到 '{video_path}'...")
    try:
        if not os.path.exists(video_path) or not os.path.exists(new_audio_path):
            logging.error(f"输入文件不存在: 视频路径 '{video_path}', 音频路径 '{new_audio_path}'")
            return False

        # 获取音频和视频的时长
        audio_probe = ffmpeg.probe(new_audio_path)
        video_probe = ffmpeg.probe(video_path)

        audio_duration = float(audio_probe['format']['duration'])
        video_duration = float(video_probe['format']['duration'])

        logging.info(f"原视频时长: {video_duration:.2f}秒")
        logging.info(f"新音频时长: {audio_duration:.2f}秒")

        input_video = ffmpeg.input(video_path)
        input_audio = ffmpeg.input(new_audio_path)

        if audio_duration > video_duration:
            # 如果音频比视频长，需要延长视频
            logging.info(f"音频比视频长 {audio_duration - video_duration:.2f}秒，将延长视频以匹配音频长度")

            extend_duration = audio_duration - video_duration

            # 获取视频帧率信息
            video_stream = next(s for s in video_probe['streams'] if s['codec_type'] == 'video')
            fps_str = video_stream.get('r_frame_rate', '30/1')
            fps = eval(fps_str)  # 例如 "30/1" -> 30.0

            # 计算倒数第5帧的时间点
            frame_duration = 1.0 / fps
            fifth_last_frame_time = video_duration - (5 * frame_duration)

            logging.info(f"视频帧率: {fps} fps")
            logging.info(f"倒数第5帧时间点: {fifth_last_frame_time:.3f}秒")

            # 方法：提取倒数第5帧并延长视频
            # 1. 先截取到倒数第5帧的时间点
            trimmed_video = input_video.video.filter('trim', end=fifth_last_frame_time).filter('setpts', 'PTS-STARTPTS')

            # 2. 使用 tpad 延长视频，克隆倒数第5帧
            total_extend_duration = extend_duration + (5 * frame_duration)  # 需要延长的总时间
            extended_video = trimmed_video.filter('tpad', stop_mode='clone', stop_duration=total_extend_duration)

            (
                ffmpeg
                .output(
                    extended_video,
                    input_audio,
                    output_video_path,
                    vcodec='libx264',
                    acodec='aac'
                )
                .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
            )
        else:
            # 如果视频比音频长或相等，使用原视频长度
            logging.info("视频长度足够，使用原视频长度")

            (
                ffmpeg
                .output(
                    input_video.video,
                    input_audio,
                    output_video_path,
                    vcodec='copy',  # 如果不需要延长，可以直接复制视频流
                    acodec='aac'
                )
                .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
            )

        logging.info(f"新的视频文件已生成: '{output_video_path}'")
        return True
    except ffmpeg.Error as e:
        logging.error("替换音频时发生 ffmpeg 错误:")
        logging.error(e.stderr.decode('utf8'))
        return False
    except Exception as e:
        logging.error(f"替换音频时发生未知错误: {e}")
        return False

def normalize_audio(input_path: str, output_path: str) -> bool:
    """
    使用 ffmpeg 的 loudnorm 滤镜标准化音频响度。

    :param input_path: 输入音频文件的路径。
    :param output_path: 输出标准化音频文件的路径。
    :return: 如果成功则返回 True，否则返回 False。
    """
    logging.info(f"开始对 '{input_path}' 进行响度标准化...")
    try:
        (
            ffmpeg
            .input(input_path)
            .filter('loudnorm')
            .output(output_path)
            .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
        )
        logging.info(f"音频已成功标准化到: '{output_path}'")
        return True
    except ffmpeg.Error as e:
        logging.error("响度标准化时发生 ffmpeg 错误:")
        logging.error(e.stderr.decode('utf8'))
        return False
    except Exception as e:
        logging.error(f"响度标准化时发生未知错误: {e}")
        return False
from spleeter.separator import Separator
from pydub import AudioSegment
from pydub.silence import split_on_silence
import shutil
import api_services
import re

def separate_vocals(audio_path: str, temp_dir: str) -> (str, str):
    """
    使用 Spleeter 分离人声和背景音乐。

    :param audio_path: 输入音频文件的路径。
    :param temp_dir: 用于存放 Spleeter 输出的临时目录。
    :return: 一个包含 (人声音频路径, 背景音音频路径) 的元组，如果失败则返回 (None, None)。
    """
    logging.info(f"开始对 '{audio_path}' 进行人声分离...")
    try:
        # --- Spleeter模型缓存路径修复 ---
        # 设置环境变量，强制spleeter将模型下载到项目子目录
        model_cache_dir = os.path.join(os.getcwd(), "spleeter_models")
        os.environ['SPLEETER_MODELS_DIR'] = model_cache_dir
        os.makedirs(model_cache_dir, exist_ok=True)
        logging.info(f"Spleeter模型缓存目录设置为: {model_cache_dir}")
        # --- 修复结束 ---

        # Spleeter 会在指定的输出目录下创建一个与输入文件名同名的子目录
        spleeter_output_dir = os.path.join(temp_dir, 'spleeter_output')
        os.makedirs(spleeter_output_dir, exist_ok=True)

        separator = Separator('spleeter:2stems')
        separator.separate_to_file(audio_path, spleeter_output_dir)
        logging.info(f"Spleeter processing finished for '{audio_path}'. Checking for output files...")
        
        input_filename_base = os.path.splitext(os.path.basename(audio_path))[0]
        original_vocals_path = os.path.join(spleeter_output_dir, input_filename_base, 'vocals.wav')
        original_accompaniment_path = os.path.join(spleeter_output_dir, input_filename_base, 'accompaniment.wav')
        
        if os.path.exists(original_vocals_path) and os.path.exists(original_accompaniment_path):
            # 将结果移动到临时目录的顶层，方便管理
            final_vocals_path = os.path.join(temp_dir, "vocals.wav")
            final_accompaniment_path = os.path.join(temp_dir, "accompaniment.wav")
            shutil.move(original_vocals_path, final_vocals_path)
            shutil.move(original_accompaniment_path, final_accompaniment_path)
            
            # 清理spleeter创建的子目录
            shutil.rmtree(os.path.join(spleeter_output_dir, input_filename_base))

            logging.info(f"人声和背景音文件已成功生成: '{final_vocals_path}', '{final_accompaniment_path}'")
            return final_vocals_path, final_accompaniment_path
        else:
            logging.error(f"Spleeter 处理后未找到期望的输出文件。")
            return None, None

    except Exception as e:
        logging.error(f"使用 Spleeter 进行人声分离时发生错误: {e}", exc_info=True)
        return None, None

def split_audio_on_silence(
    audio_path: str,
    output_dir: str,
    min_silence_len=250,      # 最终调整：识别更短的停顿
    silence_thresh=-45,       # 最终调整：避免切掉轻微人声
    keep_silence=300,         # 最终调整：为片段保留更多上下文
    max_chunk_duration_ms=12000 # 保持最大切片时长作为保险
) -> list:
    """
    根据静音部分将音频文件分割成多个片段，并确保每个片段不超过最大时长。

    :param audio_path: 输入音频文件的路径。
    :param output_dir: 切片后音频文件的保存目录。
    :param min_silence_len: 最小静音长度（毫秒），用于判断切分点。
    :param silence_thresh: 静音阈值（dBFS），低于此值被认为是静音。
    :param keep_silence: 在每个切片的两端保留的静音长度（毫秒）。
    :param max_chunk_duration_ms: 单个音频切片的最大时长（毫秒）。
    :return: 一个包含切片文件路径和精确起止时间（毫秒）的字典列表。
    """
    logging.info(f"开始根据静音切分音频: {audio_path}")
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        audio = AudioSegment.from_wav(audio_path)
        
        from pydub.silence import detect_nonsilent
        
        nonsilent_ranges = detect_nonsilent(
            audio,
            min_silence_len=min_silence_len,
            silence_thresh=silence_thresh
        )

        if not nonsilent_ranges:
            logging.warning("在音频中未检测到非静音部分。")
            return []

        # -- 新增逻辑：处理过长的切片 --
        processed_ranges = []
        for start_ms, end_ms in nonsilent_ranges:
            duration = end_ms - start_ms
            if duration > max_chunk_duration_ms:
                logging.info(f"发现长片段 ({(duration / 1000):.2f}s)，将按最大 {max_chunk_duration_ms/1000}s 进行切分...")
                current_start = start_ms
                while current_start < end_ms:
                    current_end = current_start + max_chunk_duration_ms
                    processed_ranges.append((current_start, min(current_end, end_ms)))
                    current_start = current_end
            else:
                processed_ranges.append((start_ms, end_ms))
        
        chunk_info = []
        for i, (start_ms, end_ms) in enumerate(processed_ranges):
            # 扩展切片以包含一些上下文，但不修改原始时间戳
            start_ms_extended = max(0, start_ms - keep_silence)
            end_ms_extended = min(len(audio), end_ms + keep_silence)
            
            chunk = audio[start_ms_extended:end_ms_extended]
            
            # 使用原始的、未扩展的时间戳命名
            chunk_filename = f"chunk_{start_ms:08d}_{end_ms:08d}.wav"
            chunk_path = os.path.join(output_dir, chunk_filename)
            
            # 导出为百度ASR兼容的格式
            chunk.export(chunk_path, format="wav", parameters=["-ac", "1", "-ar", "16000", "-acodec", "pcm_s16le"])
            
            chunk_info.append({
                "path": chunk_path,
                "start_ms": start_ms, # 使用原始的、未扩展的时间戳
                "end_ms": end_ms      # 使用原始的、未扩展的时间戳
            })

        logging.info(f"音频成功切分为 {len(chunk_info)} 个初始片段。")
        
        # -- 新增逻辑：合并短小的、相邻的片段 --
        merged_chunks = []
        i = 0
        while i < len(chunk_info):
            current_chunk = chunk_info[i]
            
            # 检查是否可以与下一个片段合并
            if i + 1 < len(chunk_info):
                next_chunk = chunk_info[i+1]
                
                current_duration = current_chunk['end_ms'] - current_chunk['start_ms']
                gap_duration = next_chunk['start_ms'] - current_chunk['end_ms']
                # 当前片段长度和下一个片段长度都小于1.5s且与下一个片段的间隙小于0.5s时合并
                if current_duration < 1500 and next_chunk['end_ms'] - next_chunk['start_ms'] < 1500 and gap_duration < 500:
                    logging.info(f"合并片段: {os.path.basename(current_chunk['path'])} 和 {os.path.basename(next_chunk['path'])}")
                    
                    # 合并音频文件
                    current_audio = AudioSegment.from_wav(current_chunk['path'])
                    next_audio = AudioSegment.from_wav(next_chunk['path'])
                    
                    # 创建一个与间隙等长的静音
                    silence_gap = AudioSegment.silent(duration=gap_duration)
                    
                    merged_audio = current_audio + silence_gap + next_audio
                    
                    # 创建新的合并后的片段信息
                    merged_chunk_info = {
                        "start_ms": current_chunk['start_ms'],
                        "end_ms": next_chunk['end_ms'],
                    }
                    
                    # 保存新的合并后的音频文件
                    merged_filename = f"merged_{merged_chunk_info['start_ms']:08d}_{merged_chunk_info['end_ms']:08d}.wav"
                    merged_path = os.path.join(output_dir, merged_filename)
                    merged_audio.export(merged_path, format="wav", parameters=["-ac", "1", "-ar", "16000", "-acodec", "pcm_s16le"])
                    merged_chunk_info['path'] = merged_path
                    
                    merged_chunks.append(merged_chunk_info)
                    
                    # 删除旧的、未合并的片段文件
                    os.remove(current_chunk['path'])
                    os.remove(next_chunk['path'])
                    
                    i += 2 # 跳过下一个片段，因为它已经被合并了
                    continue
            
            # 如果不合并，则直接添加当前片段
            merged_chunks.append(current_chunk)
            i += 1
            
        logging.info(f"合并后，最终生成 {len(merged_chunks)} 个音频片段。")

        # -- 新增逻辑：基于语义的智能合并 --
        semantic_merged_chunks = smart_semantic_merge(merged_chunks)

        return semantic_merged_chunks

    except Exception as e:
        logging.error(f"切分或合并音频时发生错误: {e}", exc_info=True)
        return []


def smart_semantic_merge(chunks: list, min_duration_ms: int = 1500) -> list:
    """
    基于语音识别和语义分析的智能合并短语音分段

    :param chunks: 音频片段列表
    :param min_duration_ms: 最小片段时长阈值（毫秒）
    :return: 智能合并后的音频片段列表
    """
    if len(chunks) <= 1:
        return chunks

    logging.info("开始基于语义的智能合并...")

    # 1. 识别所有短片段
    short_chunks = []
    for i, chunk in enumerate(chunks):
        duration = chunk['end_ms'] - chunk['start_ms']
        if duration < min_duration_ms:
            short_chunks.append(i)

    if not short_chunks:
        logging.info("没有发现需要智能合并的短片段")
        return chunks

    logging.info(f"发现 {len(short_chunks)} 个短片段需要智能合并")

    # 2. 对短片段进行语音识别和语义分析
    result_chunks = chunks.copy()

    # 重新实现：逐个处理短片段，每次处理后重新计算索引
    changes_made = True
    iteration = 0
    max_iterations = 10  # 防止无限循环

    while changes_made and iteration < max_iterations:
        changes_made = False
        iteration += 1

        logging.info(f"智能合并第 {iteration} 轮...")

        # 重新识别短片段
        current_short_chunks = []
        for i, chunk in enumerate(result_chunks):
            duration = chunk['end_ms'] - chunk['start_ms']
            if duration < min_duration_ms:
                current_short_chunks.append(i)

        if not current_short_chunks:
            break

        # 处理第一个短片段
        short_idx = current_short_chunks[0]

        if short_idx >= len(result_chunks):
            break

        current_chunk = result_chunks[short_idx]
        prev_chunk = result_chunks[short_idx - 1] if short_idx > 0 else None
        next_chunk = result_chunks[short_idx + 1] if short_idx < len(result_chunks) - 1 else None

        # 语音识别当前片段
        current_text = recognize_audio_text(current_chunk['path'])
        if not current_text:
            logging.warning(f"短片段 {short_idx} 语音识别失败，使用基于时间间隔的合并策略")
            # 备用策略：基于时间间隔的合并
            merge_direction = decide_merge_by_timing(current_chunk, prev_chunk, next_chunk)
        else:
            # 决定合并方向
            merge_direction = decide_merge_direction(
                current_text, current_chunk,
                prev_chunk, next_chunk
            )

            # 决定合并方向
            merge_direction = decide_merge_direction(
                current_text, current_chunk,
                prev_chunk, next_chunk
            )

        if merge_direction == "prev" and prev_chunk and short_idx > 0:
            # 与前一个片段合并
            result_chunks = merge_chunks_at_indices(result_chunks, short_idx - 1, short_idx)
            logging.info(f"短片段 {short_idx} 与前一个片段合并: '{current_text}'")
            changes_made = True

        elif merge_direction == "next" and next_chunk and short_idx < len(result_chunks) - 1:
            # 与后一个片段合并
            result_chunks = merge_chunks_at_indices(result_chunks, short_idx, short_idx + 1)
            logging.info(f"短片段 {short_idx} 与后一个片段合并: '{current_text}'")
            changes_made = True

        else:
            logging.info(f"短片段 {short_idx} 保持独立: '{current_text}'")
            # 标记为已处理，避免重复处理
            result_chunks[short_idx]['_processed'] = True

    logging.info(f"智能合并完成，最终生成 {len(result_chunks)} 个音频片段")
    return result_chunks


def recognize_audio_text(audio_path: str) -> str:
    """
    识别音频片段的文本内容

    :param audio_path: 音频文件路径
    :return: 识别的文本，失败返回空字符串
    """
    try:
        asr_result = api_services.speech_to_text(audio_path)
        if asr_result and asr_result.get("result"):
            return asr_result["result"][0].strip()
    except Exception as e:
        logging.error(f"语音识别失败 {audio_path}: {e}")
    return ""


def decide_merge_by_timing(current_chunk: dict, prev_chunk: dict, next_chunk: dict) -> str:
    """
    基于时间间隔的备用合并策略（当语音识别失败时使用）

    :param current_chunk: 当前片段信息
    :param prev_chunk: 前一个片段信息
    :param next_chunk: 后一个片段信息
    :return: "prev", "next", 或 "none"
    """

    # 计算与前后片段的时间间隔
    gap_to_prev = float('inf')
    gap_to_next = float('inf')

    if prev_chunk:
        gap_to_prev = current_chunk['start_ms'] - prev_chunk['end_ms']

    if next_chunk:
        gap_to_next = next_chunk['start_ms'] - current_chunk['end_ms']

    # 优先与间隔更短的片段合并
    if gap_to_prev < gap_to_next and gap_to_prev < 800:  # 间隔小于0.8秒
        return "prev" if prev_chunk else "none"
    elif gap_to_next < gap_to_prev and gap_to_next < 800:  # 间隔小于0.8秒
        return "next" if next_chunk else "none"
    elif gap_to_prev < 500:  # 如果与前面间隔很短（<0.5秒）
        return "prev" if prev_chunk else "none"
    elif gap_to_next < 500:  # 如果与后面间隔很短（<0.5秒）
        return "next" if next_chunk else "none"

    return "none"


def decide_merge_direction(current_text: str, current_chunk: dict,
                         prev_chunk: dict, next_chunk: dict) -> str:
    """
    基于语义分析决定短片段的合并方向

    :param current_text: 当前短片段的文本
    :param current_chunk: 当前片段信息
    :param prev_chunk: 前一个片段信息
    :param next_chunk: 后一个片段信息
    :param current_idx: 当前片段索引
    :param all_chunks: 所有片段列表
    :return: "prev", "next", 或 "none"
    """

    # 1. 基于文本特征的规则判断

    # 如果是单个词或很短的内容，倾向于与前面合并
    if len(current_text.split()) <= 2 or len(current_text) <= 5:
        # 检查是否是常见的连接词、介词等
        connecting_words = ["and", "or", "but", "the", "a", "an", "in", "on", "at", "to", "for", "of", "with"]
        if any(word.lower() in current_text.lower() for word in connecting_words):
            return "prev" if prev_chunk else "next"

    # 2. 检查标点符号和语句完整性

    # 如果当前文本以标点结尾，倾向于与前面合并
    if re.search(r'[.!?]$', current_text.strip()):
        return "prev" if prev_chunk else "none"

    # 如果当前文本以连词开始，倾向于与前面合并
    if re.match(r'^(and|or|but|so|because|however|therefore)\b', current_text.lower()):
        return "prev" if prev_chunk else "none"

    # 如果当前文本看起来是句子的开始，倾向于与后面合并
    if re.match(r'^[A-Z]', current_text) and not re.search(r'[.!?]$', current_text.strip()):
        return "next" if next_chunk else "none"

    # 3. 基于上下文的语义分析

    prev_text = ""
    next_text = ""

    # 获取前后片段的文本
    if prev_chunk:
        prev_text = recognize_audio_text(prev_chunk['path'])

    if next_chunk:
        next_text = recognize_audio_text(next_chunk['path'])

    # 计算与前后文本的语义相关性
    prev_score = calculate_semantic_similarity(current_text, prev_text)
    next_score = calculate_semantic_similarity(current_text, next_text)

    # 4. 综合决策

    # 如果与前文相关性更高，且前文不以句号结尾
    if prev_score > next_score and prev_text and not re.search(r'[.!?]$', prev_text.strip()):
        return "prev" if prev_chunk else "none"

    # 如果与后文相关性更高，且当前文本不以句号结尾
    if next_score > prev_score and next_text and not re.search(r'[.!?]$', current_text.strip()):
        return "next" if next_chunk else "none"

    # 默认情况：如果前面存在且时间间隔较短，倾向于与前面合并
    if prev_chunk:
        gap_to_prev = current_chunk['start_ms'] - prev_chunk['end_ms']
        if gap_to_prev < 800:  # 间隔小于0.8秒
            return "prev"

    # 否则尝试与后面合并
    if next_chunk:
        gap_to_next = next_chunk['start_ms'] - current_chunk['end_ms']
        if gap_to_next < 800:  # 间隔小于0.8秒
            return "next"

    return "none"


def calculate_semantic_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本的语义相似性（简化版本）

    :param text1: 文本1
    :param text2: 文本2
    :return: 相似性分数 (0-1)
    """
    if not text1 or not text2:
        return 0.0

    # 简单的词汇重叠度计算
    words1 = set(re.findall(r'\b\w+\b', text1.lower()))
    words2 = set(re.findall(r'\b\w+\b', text2.lower()))

    if not words1 or not words2:
        return 0.0

    intersection = words1.intersection(words2)
    union = words1.union(words2)

    return len(intersection) / len(union) if union else 0.0


def merge_chunks_at_indices(chunks: list, idx1: int, idx2: int) -> list:
    """
    合并指定索引的两个音频片段

    :param chunks: 音频片段列表
    :param idx1: 第一个片段索引
    :param idx2: 第二个片段索引
    :return: 合并后的片段列表
    """
    if idx1 > idx2:
        idx1, idx2 = idx2, idx1

    if idx1 < 0 or idx2 >= len(chunks) or idx2 != idx1 + 1:
        logging.error(f"无效的合并索引: {idx1}, {idx2}")
        return chunks

    chunk1 = chunks[idx1]
    chunk2 = chunks[idx2]

    try:
        # 加载音频文件
        audio1 = AudioSegment.from_wav(chunk1['path'])
        audio2 = AudioSegment.from_wav(chunk2['path'])

        # 计算间隙
        gap_duration = chunk2['start_ms'] - chunk1['end_ms']

        # 创建合并的音频
        if gap_duration > 0:
            silence_gap = AudioSegment.silent(duration=gap_duration)
            merged_audio = audio1 + silence_gap + audio2
        else:
            merged_audio = audio1 + audio2

        # 创建新的合并片段信息
        merged_chunk = {
            "start_ms": chunk1['start_ms'],
            "end_ms": chunk2['end_ms'],
        }

        # 保存合并后的音频文件
        output_dir = os.path.dirname(chunk1['path'])
        merged_filename = f"semantic_merged_{merged_chunk['start_ms']:08d}_{merged_chunk['end_ms']:08d}.wav"
        merged_path = os.path.join(output_dir, merged_filename)

        merged_audio.export(merged_path, format="wav", parameters=["-ac", "1", "-ar", "16000", "-acodec", "pcm_s16le"])
        merged_chunk['path'] = merged_path

        # 删除原始文件
        try:
            os.remove(chunk1['path'])
            os.remove(chunk2['path'])
        except OSError as e:
            logging.warning(f"删除原始文件失败: {e}")

        # 创建新的片段列表
        new_chunks = chunks[:idx1] + [merged_chunk] + chunks[idx2+1:]

        return new_chunks

    except Exception as e:
        logging.error(f"合并音频片段失败: {e}")
        return chunks