'''
Author: mobolun <EMAIL>
Date: 2025-07-25 01:15:30
LastEditors: mobolun <EMAIL>
LastEditTime: 2025-07-26 16:41:37
FilePath: \VedioLocal\video_processor.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
# -*- coding: utf-8 -*-

import ffmpeg
import logging
import os

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def extract_audio(video_path: str, output_audio_path: str) -> bool:
    """
    从视频文件中提取音频。

    :param video_path: 输入视频文件的路径。
    :param output_audio_path: 输出音频文件的保存路径。
    :return: 如果成功则返回 True，否则返回 False。
    """
    logging.info(f"开始从 '{video_path}' 提取音频...")
    try:
        if not os.path.exists(video_path):
            logging.error(f"视频文件不存在: {video_path}")
            return False
            
        (
            ffmpeg
            .input(video_path)
            .output(
                output_audio_path,
                acodec='pcm_s16le',  # 百度ASR推荐的编码
                ac=1,                # 单声道
                ar='16000'           # 16k 采样率
            )
            .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
        )
        logging.info(f"音频已成功提取到: '{output_audio_path}'")
        return True
    except ffmpeg.Error as e:
        logging.error("提取音频时发生 ffmpeg 错误:")
        logging.error(e.stderr.decode('utf8'))
        return False
    except Exception as e:
        logging.error(f"提取音频时发生未知错误: {e}")
        return False

def replace_audio(video_path: str, new_audio_path: str, output_video_path: str) -> bool:
    """
    用新的音轨替换视频中的原始音轨，确保视频长度能够容纳完整的音频播放。

    :param video_path: 原始视频文件的路径。
    :param new_audio_path: 新音频文件的路径。
    :param output_video_path: 输出视频文件的保存路径。
    :return: 如果成功则返回 True，否则返回 False。
    """
    logging.info(f"开始将 '{new_audio_path}' 的音频合并到 '{video_path}'...")
    try:
        if not os.path.exists(video_path) or not os.path.exists(new_audio_path):
            logging.error(f"输入文件不存在: 视频路径 '{video_path}', 音频路径 '{new_audio_path}'")
            return False

        # 获取音频和视频的时长
        audio_probe = ffmpeg.probe(new_audio_path)
        video_probe = ffmpeg.probe(video_path)

        audio_duration = float(audio_probe['format']['duration'])
        video_duration = float(video_probe['format']['duration'])

        logging.info(f"原视频时长: {video_duration:.2f}秒")
        logging.info(f"新音频时长: {audio_duration:.2f}秒")

        input_video = ffmpeg.input(video_path)
        input_audio = ffmpeg.input(new_audio_path)

        if audio_duration > video_duration:
            # 如果音频比视频长，需要延长视频
            logging.info(f"音频比视频长 {audio_duration - video_duration:.2f}秒，将延长视频以匹配音频长度")

            # 使用更稳定的方法：先延长视频，然后与音频合并
            extend_duration = audio_duration - video_duration

            # 方法：使用 tpad 延长视频，确保最后一帧被正确克隆
            extended_video = input_video.video.filter('tpad', stop_mode='clone', stop_duration=extend_duration)

            # 简化方法，避免复杂的映射参数
            (
                ffmpeg
                .output(
                    extended_video,
                    input_audio,
                    output_video_path,
                    vcodec='libx264',
                    acodec='aac'
                )
                .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
            )
        else:
            # 如果视频比音频长或相等，使用原视频长度
            logging.info("视频长度足够，使用原视频长度")

            (
                ffmpeg
                .output(
                    input_video.video,
                    input_audio,
                    output_video_path,
                    vcodec='copy',  # 如果不需要延长，可以直接复制视频流
                    acodec='aac'
                )
                .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
            )

        logging.info(f"新的视频文件已生成: '{output_video_path}'")
        return True
    except ffmpeg.Error as e:
        logging.error("替换音频时发生 ffmpeg 错误:")
        logging.error(e.stderr.decode('utf8'))
        return False
    except Exception as e:
        logging.error(f"替换音频时发生未知错误: {e}")
        return False

def normalize_audio(input_path: str, output_path: str) -> bool:
    """
    使用 ffmpeg 的 loudnorm 滤镜标准化音频响度。

    :param input_path: 输入音频文件的路径。
    :param output_path: 输出标准化音频文件的路径。
    :return: 如果成功则返回 True，否则返回 False。
    """
    logging.info(f"开始对 '{input_path}' 进行响度标准化...")
    try:
        (
            ffmpeg
            .input(input_path)
            .filter('loudnorm')
            .output(output_path)
            .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
        )
        logging.info(f"音频已成功标准化到: '{output_path}'")
        return True
    except ffmpeg.Error as e:
        logging.error("响度标准化时发生 ffmpeg 错误:")
        logging.error(e.stderr.decode('utf8'))
        return False
    except Exception as e:
        logging.error(f"响度标准化时发生未知错误: {e}")
        return False
from spleeter.separator import Separator
from pydub import AudioSegment
from pydub.silence import split_on_silence
import shutil

def separate_vocals(audio_path: str, temp_dir: str) -> (str, str):
    """
    使用 Spleeter 分离人声和背景音乐。

    :param audio_path: 输入音频文件的路径。
    :param temp_dir: 用于存放 Spleeter 输出的临时目录。
    :return: 一个包含 (人声音频路径, 背景音音频路径) 的元组，如果失败则返回 (None, None)。
    """
    logging.info(f"开始对 '{audio_path}' 进行人声分离...")
    try:
        # --- Spleeter模型缓存路径修复 ---
        # 设置环境变量，强制spleeter将模型下载到项目子目录
        model_cache_dir = os.path.join(os.getcwd(), "spleeter_models")
        os.environ['SPLEETER_MODELS_DIR'] = model_cache_dir
        os.makedirs(model_cache_dir, exist_ok=True)
        logging.info(f"Spleeter模型缓存目录设置为: {model_cache_dir}")
        # --- 修复结束 ---

        # Spleeter 会在指定的输出目录下创建一个与输入文件名同名的子目录
        spleeter_output_dir = os.path.join(temp_dir, 'spleeter_output')
        os.makedirs(spleeter_output_dir, exist_ok=True)

        separator = Separator('spleeter:2stems')
        separator.separate_to_file(audio_path, spleeter_output_dir)
        logging.info(f"Spleeter processing finished for '{audio_path}'. Checking for output files...")
        
        input_filename_base = os.path.splitext(os.path.basename(audio_path))[0]
        original_vocals_path = os.path.join(spleeter_output_dir, input_filename_base, 'vocals.wav')
        original_accompaniment_path = os.path.join(spleeter_output_dir, input_filename_base, 'accompaniment.wav')
        
        if os.path.exists(original_vocals_path) and os.path.exists(original_accompaniment_path):
            # 将结果移动到临时目录的顶层，方便管理
            final_vocals_path = os.path.join(temp_dir, "vocals.wav")
            final_accompaniment_path = os.path.join(temp_dir, "accompaniment.wav")
            shutil.move(original_vocals_path, final_vocals_path)
            shutil.move(original_accompaniment_path, final_accompaniment_path)
            
            # 清理spleeter创建的子目录
            shutil.rmtree(os.path.join(spleeter_output_dir, input_filename_base))

            logging.info(f"人声和背景音文件已成功生成: '{final_vocals_path}', '{final_accompaniment_path}'")
            return final_vocals_path, final_accompaniment_path
        else:
            logging.error(f"Spleeter 处理后未找到期望的输出文件。")
            return None, None

    except Exception as e:
        logging.error(f"使用 Spleeter 进行人声分离时发生错误: {e}", exc_info=True)
        return None, None

def split_audio_on_silence(
    audio_path: str,
    output_dir: str,
    min_silence_len=250,      # 最终调整：识别更短的停顿
    silence_thresh=-45,       # 最终调整：避免切掉轻微人声
    keep_silence=300,         # 最终调整：为片段保留更多上下文
    max_chunk_duration_ms=12000 # 保持最大切片时长作为保险
) -> list:
    """
    根据静音部分将音频文件分割成多个片段，并确保每个片段不超过最大时长。

    :param audio_path: 输入音频文件的路径。
    :param output_dir: 切片后音频文件的保存目录。
    :param min_silence_len: 最小静音长度（毫秒），用于判断切分点。
    :param silence_thresh: 静音阈值（dBFS），低于此值被认为是静音。
    :param keep_silence: 在每个切片的两端保留的静音长度（毫秒）。
    :param max_chunk_duration_ms: 单个音频切片的最大时长（毫秒）。
    :return: 一个包含切片文件路径和精确起止时间（毫秒）的字典列表。
    """
    logging.info(f"开始根据静音切分音频: {audio_path}")
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        audio = AudioSegment.from_wav(audio_path)
        
        from pydub.silence import detect_nonsilent
        
        nonsilent_ranges = detect_nonsilent(
            audio,
            min_silence_len=min_silence_len,
            silence_thresh=silence_thresh
        )

        if not nonsilent_ranges:
            logging.warning("在音频中未检测到非静音部分。")
            return []

        # -- 新增逻辑：处理过长的切片 --
        processed_ranges = []
        for start_ms, end_ms in nonsilent_ranges:
            duration = end_ms - start_ms
            if duration > max_chunk_duration_ms:
                logging.info(f"发现长片段 ({(duration / 1000):.2f}s)，将按最大 {max_chunk_duration_ms/1000}s 进行切分...")
                current_start = start_ms
                while current_start < end_ms:
                    current_end = current_start + max_chunk_duration_ms
                    processed_ranges.append((current_start, min(current_end, end_ms)))
                    current_start = current_end
            else:
                processed_ranges.append((start_ms, end_ms))
        
        chunk_info = []
        for i, (start_ms, end_ms) in enumerate(processed_ranges):
            # 扩展切片以包含一些上下文，但不修改原始时间戳
            start_ms_extended = max(0, start_ms - keep_silence)
            end_ms_extended = min(len(audio), end_ms + keep_silence)
            
            chunk = audio[start_ms_extended:end_ms_extended]
            
            # 使用原始的、未扩展的时间戳命名
            chunk_filename = f"chunk_{start_ms:08d}_{end_ms:08d}.wav"
            chunk_path = os.path.join(output_dir, chunk_filename)
            
            # 导出为百度ASR兼容的格式
            chunk.export(chunk_path, format="wav", parameters=["-ac", "1", "-ar", "16000", "-acodec", "pcm_s16le"])
            
            chunk_info.append({
                "path": chunk_path,
                "start_ms": start_ms, # 使用原始的、未扩展的时间戳
                "end_ms": end_ms      # 使用原始的、未扩展的时间戳
            })

        logging.info(f"音频成功切分为 {len(chunk_info)} 个片段。")
        return chunk_info

    except Exception as e:
        logging.error(f"切分音频时发生错误: {e}", exc_info=True)
        return []