#!/usr/bin/env python3
"""
测试视频延长功能，检查是否出现黑屏
"""
import os
import ffmpeg
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_video_extension_methods():
    """测试不同的视频延长方法"""
    
    video_path = "01. Installing and Opening Blender for the First Time Mammoth.mp4"
    
    if not os.path.exists(video_path):
        logging.error(f"视频文件不存在: {video_path}")
        return
    
    # 获取原视频信息
    try:
        probe = ffmpeg.probe(video_path)
        video_duration = float(probe['format']['duration'])
        logging.info(f"原视频时长: {video_duration:.2f}秒")
    except Exception as e:
        logging.error(f"获取视频信息失败: {e}")
        return
    
    # 测试延长5秒
    extend_duration = 5.0
    target_duration = video_duration + extend_duration
    
    logging.info(f"将视频延长 {extend_duration}秒，目标时长: {target_duration:.2f}秒")
    
    # 方法1: 当前使用的 tpad 方法
    test_tpad_method(video_path, extend_duration, target_duration)
    
    # 方法2: 改进的 tpad 方法
    test_improved_tpad_method(video_path, extend_duration, target_duration)

def test_tpad_method(video_path, extend_duration, target_duration):
    """测试当前的 tpad 方法"""
    output_path = "test_tpad_current.mp4"
    
    logging.info("\n=== 测试当前 tpad 方法 ===")
    
    try:
        input_video = ffmpeg.input(video_path)
        
        # 当前方法
        extended_video = input_video.video.filter('tpad', stop_mode='clone', stop_duration=extend_duration)
        
        (
            ffmpeg
            .output(
                extended_video,
                input_video.audio,  # 使用原音频进行测试
                output_path,
                vcodec='libx264',
                acodec='aac',
                t=target_duration
            )
            .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
        )
        
        if os.path.exists(output_path):
            probe = ffmpeg.probe(output_path)
            actual_duration = float(probe['format']['duration'])
            logging.info(f"✓ 当前方法成功，实际时长: {actual_duration:.2f}秒")
        else:
            logging.error("✗ 当前方法失败")
            
    except ffmpeg.Error as e:
        logging.error(f"✗ 当前方法失败: {e.stderr.decode('utf8')}")
    except Exception as e:
        logging.error(f"✗ 当前方法失败: {e}")

def test_improved_tpad_method(video_path, extend_duration, target_duration):
    """测试改进的 tpad 方法"""
    output_path = "test_tpad_improved.mp4"
    
    logging.info("\n=== 测试改进 tpad 方法 ===")
    
    try:
        input_video = ffmpeg.input(video_path)
        
        # 改进方法：使用更明确的参数
        extended_video = input_video.video.filter('tpad', stop_mode='clone', stop_duration=extend_duration)
        
        # 不使用 t 参数，让视频自然延长
        (
            ffmpeg
            .output(
                extended_video,
                input_video.audio,  # 使用原音频进行测试
                output_path,
                vcodec='libx264',
                acodec='aac'
                # 移除 t=target_duration 参数
            )
            .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
        )
        
        if os.path.exists(output_path):
            probe = ffmpeg.probe(output_path)
            actual_duration = float(probe['format']['duration'])
            logging.info(f"✓ 改进方法成功，实际时长: {actual_duration:.2f}秒")
        else:
            logging.error("✗ 改进方法失败")
            
    except ffmpeg.Error as e:
        logging.error(f"✗ 改进方法失败: {e.stderr.decode('utf8')}")
    except Exception as e:
        logging.error(f"✗ 改进方法失败: {e}")

def test_loop_method(video_path, extend_duration, target_duration):
    """测试使用 loop 滤镜的方法"""
    output_path = "test_loop_method.mp4"
    
    logging.info("\n=== 测试 loop 方法 ===")
    
    try:
        input_video = ffmpeg.input(video_path)
        
        # 使用 loop 滤镜重复最后一帧
        # 首先获取视频的帧率
        probe = ffmpeg.probe(video_path)
        video_stream = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        fps = eval(video_stream['r_frame_rate'])  # 例如 "30/1" -> 30.0
        
        # 计算需要循环的帧数
        loop_frames = int(extend_duration * fps)
        
        # 使用 loop 滤镜
        extended_video = input_video.video.filter('loop', loop=loop_frames, size=1, start=-1)
        
        (
            ffmpeg
            .output(
                extended_video,
                input_video.audio,
                output_path,
                vcodec='libx264',
                acodec='aac'
            )
            .run(overwrite_output=True, capture_stdout=True, capture_stderr=True)
        )
        
        if os.path.exists(output_path):
            probe = ffmpeg.probe(output_path)
            actual_duration = float(probe['format']['duration'])
            logging.info(f"✓ loop 方法成功，实际时长: {actual_duration:.2f}秒")
        else:
            logging.error("✗ loop 方法失败")
            
    except ffmpeg.Error as e:
        logging.error(f"✗ loop 方法失败: {e.stderr.decode('utf8')}")
    except Exception as e:
        logging.error(f"✗ loop 方法失败: {e}")

if __name__ == "__main__":
    test_video_extension_methods()
