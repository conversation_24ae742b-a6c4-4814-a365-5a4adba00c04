#!/usr/bin/env python3
"""
测试增强的智能语义合并功能
专门测试像 "of blender" 这样的短片段合并
"""
import os
import logging
from video_processor import smart_semantic_merge, recognize_audio_text, decide_merge_direction_enhanced

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_enhanced_semantic_merge():
    """测试增强的语义合并功能"""
    
    # 使用之前生成的音频片段
    chunks_dir = "test_semantic_merge_output/chunks"
    
    if not os.path.exists(chunks_dir):
        logging.error(f"测试目录不存在: {chunks_dir}")
        return False
    
    logging.info("开始测试增强的智能语义合并...")
    
    # 1. 收集所有音频片段
    chunk_files = []
    for filename in os.listdir(chunks_dir):
        if filename.endswith('.wav'):
            chunk_path = os.path.join(chunks_dir, filename)
            
            # 从文件名解析时间信息
            if filename.startswith('chunk_') or filename.startswith('merged_') or filename.startswith('semantic_merged_'):
                try:
                    # 提取时间戳
                    parts = filename.replace('.wav', '').split('_')
                    if len(parts) >= 3:
                        start_ms = int(parts[-2])
                        end_ms = int(parts[-1])
                        
                        chunk_files.append({
                            'path': chunk_path,
                            'start_ms': start_ms,
                            'end_ms': end_ms,
                            'filename': filename
                        })
                except ValueError:
                    continue
    
    # 按时间排序
    chunk_files.sort(key=lambda x: x['start_ms'])
    
    logging.info(f"找到 {len(chunk_files)} 个音频片段")
    
    # 2. 分析所有片段，找出短片段和可能的合并案例
    short_chunks = []
    all_chunks_info = []
    
    for i, chunk in enumerate(chunk_files):
        duration = chunk['end_ms'] - chunk['start_ms']
        text = recognize_audio_text(chunk['path'])
        
        chunk_info = {
            'index': i,
            'chunk': chunk,
            'duration': duration,
            'text': text,
            'is_short': duration < 1500
        }
        
        all_chunks_info.append(chunk_info)
        
        if duration < 1500:
            short_chunks.append(chunk_info)
            logging.info(f"短片段 {i}: {duration}ms - '{text}' - {chunk['filename']}")
    
    logging.info(f"总片段数: {len(all_chunks_info)}")
    logging.info(f"短片段数: {len(short_chunks)}")
    
    # 3. 测试增强的决策逻辑
    logging.info("\n=== 测试增强的决策逻辑 ===")
    
    decision_tests = []
    
    for short_info in short_chunks[:10]:  # 只测试前10个短片段
        idx = short_info['index']
        current_chunk = short_info['chunk']
        current_text = short_info['text']
        
        prev_chunk = chunk_files[idx - 1] if idx > 0 else None
        next_chunk = chunk_files[idx + 1] if idx < len(chunk_files) - 1 else None
        
        if current_text:  # 只测试能识别的片段
            decision = decide_merge_direction_enhanced(
                current_text, current_chunk, prev_chunk, next_chunk
            )
            
            prev_text = recognize_audio_text(prev_chunk['path']) if prev_chunk else ""
            next_text = recognize_audio_text(next_chunk['path']) if next_chunk else ""
            
            test_result = {
                'index': idx,
                'current_text': current_text,
                'prev_text': prev_text,
                'next_text': next_text,
                'decision': decision,
                'duration': short_info['duration']
            }
            
            decision_tests.append(test_result)
            
            logging.info(f"\n片段 {idx}: '{current_text}' ({short_info['duration']}ms)")
            logging.info(f"  前文: '{prev_text}'")
            logging.info(f"  后文: '{next_text}'")
            logging.info(f"  决策: {decision}")
            
            if decision == "prev" and prev_text:
                combined = f"{prev_text} {current_text}"
                logging.info(f"  合并结果: '{combined}'")
            elif decision == "next" and next_text:
                combined = f"{current_text} {next_text}"
                logging.info(f"  合并结果: '{combined}'")
    
    # 4. 执行完整的智能合并
    logging.info("\n=== 执行完整的智能合并 ===")
    
    before_count = len(chunk_files)
    before_short_count = len(short_chunks)
    
    merged_chunks = smart_semantic_merge(chunk_files, min_duration_ms=1500)
    
    after_count = len(merged_chunks)
    after_short_chunks = [chunk for chunk in merged_chunks if (chunk['end_ms'] - chunk['start_ms']) < 1500]
    after_short_count = len(after_short_chunks)
    
    # 5. 分析结果
    logging.info("\n=== 合并结果分析 ===")
    
    logging.info(f"合并前总片段数: {before_count}")
    logging.info(f"合并后总片段数: {after_count}")
    logging.info(f"减少片段数: {before_count - after_count}")
    logging.info(f"合并前短片段数: {before_short_count}")
    logging.info(f"合并后短片段数: {after_short_count}")
    logging.info(f"短片段减少数: {before_short_count - after_short_count}")
    
    # 计算合并效率
    merge_efficiency = (before_short_count - after_short_count) / before_short_count * 100 if before_short_count > 0 else 0
    logging.info(f"短片段合并效率: {merge_efficiency:.1f}%")
    
    # 6. 验证特定案例
    logging.info("\n=== 验证特定案例 ===")
    
    # 查找包含 "of", "in", "on" 等介词的短片段是否被正确合并
    preposition_cases = []
    for test in decision_tests:
        if any(word in test['current_text'].lower() for word in ['of', 'in', 'on', 'at', 'for', 'with', 'to']):
            preposition_cases.append(test)
    
    logging.info(f"发现 {len(preposition_cases)} 个介词短片段案例:")
    for case in preposition_cases:
        logging.info(f"  '{case['current_text']}' -> {case['decision']}")
    
    # 7. 成功标准
    success_criteria = [
        after_count < before_count,  # 总片段数减少
        after_short_count < before_short_count,  # 短片段数减少
        merge_efficiency > 50,  # 至少50%的短片段被合并
        len(preposition_cases) > 0  # 发现了介词案例
    ]
    
    success = all(success_criteria)
    
    if success:
        logging.info("✓ 增强的智能语义合并测试成功！")
        return True
    else:
        logging.error("✗ 增强的智能语义合并测试失败")
        logging.error(f"成功标准: {success_criteria}")
        return False

if __name__ == "__main__":
    success = test_enhanced_semantic_merge()
    if success:
        print("\n🎉 增强的智能语义合并测试成功！")
        print("✅ 介词短语正确识别")
        print("✅ 语义决策逻辑工作")
        print("✅ 强制合并策略有效")
        print("✅ 短片段大幅减少")
    else:
        print("\n❌ 增强的智能语义合并测试失败")
