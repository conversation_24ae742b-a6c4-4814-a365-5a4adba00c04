#!/usr/bin/env python3
"""
测试改进后的语音识别功能
专门测试 "So lighting is a very important aspect of Blender" 这类句子的识别准确性
"""
import os
import logging
from api_services import speech_to_text, post_process_recognition_result

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_improved_asr():
    """测试改进后的ASR功能"""
    
    # 使用最新生成的音频文件进行测试
    test_audio_path = "temp_output/srt_generation_20250804-170108/audio_chunks/semantic_merged_00002269_00009477.wav"
    
    if not os.path.exists(test_audio_path):
        logging.error(f"测试音频文件不存在: {test_audio_path}")
        
        # 尝试查找其他可用的音频文件
        temp_dirs = []
        if os.path.exists("temp_output"):
            for dirname in os.listdir("temp_output"):
                if dirname.startswith("srt_generation_"):
                    temp_dirs.append(dirname)
        
        if temp_dirs:
            # 使用最新的目录
            latest_dir = sorted(temp_dirs)[-1]
            chunks_dir = f"temp_output/{latest_dir}/audio_chunks"
            
            if os.path.exists(chunks_dir):
                # 查找包含 "semantic_merged" 的文件
                for filename in os.listdir(chunks_dir):
                    if filename.startswith("semantic_merged_") and filename.endswith(".wav"):
                        test_audio_path = os.path.join(chunks_dir, filename)
                        logging.info(f"使用替代测试文件: {test_audio_path}")
                        break
        
        if not os.path.exists(test_audio_path):
            logging.error("找不到可用的测试音频文件")
            return False
    
    logging.info("开始测试改进后的语音识别功能...")
    logging.info(f"测试音频文件: {test_audio_path}")
    
    # 预期的正确结果
    expected_text = "So lighting is a very important aspect of Blender"
    
    try:
        # 1. 测试改进后的语音识别
        logging.info("=== 测试改进后的语音识别 ===")
        
        result = speech_to_text(test_audio_path)
        
        if result and result.get("result"):
            recognized_text = result["result"][0]
            logging.info(f"识别结果: '{recognized_text}'")
            logging.info(f"预期结果: '{expected_text}'")
            
            # 计算相似度
            similarity = calculate_text_similarity(recognized_text.lower(), expected_text.lower())
            logging.info(f"文本相似度: {similarity:.2f}")
            
            # 检查关键词是否正确识别
            key_words = ["lighting", "important", "aspect", "blender"]
            recognized_words = recognized_text.lower().split()
            
            correct_words = 0
            for word in key_words:
                if word in recognized_words:
                    correct_words += 1
                    logging.info(f"✓ 关键词 '{word}' 识别正确")
                else:
                    logging.warning(f"✗ 关键词 '{word}' 识别错误")
            
            word_accuracy = correct_words / len(key_words) * 100
            logging.info(f"关键词准确率: {word_accuracy:.1f}%")
            
            # 2. 测试后处理修正功能
            logging.info("\n=== 测试后处理修正功能 ===")
            
            # 模拟常见的识别错误
            test_errors = [
                "so lighting is a very important aspect two under",
                "so lighting is a very important aspect to under",
                "so lighting is a very important aspect too under",
                "so lighting is a very important aspect of blender",
                "So lighting is a very important aspect off blender",
                "so lighting is a very important aspect ov render"
            ]
            
            for error_text in test_errors:
                corrected = post_process_recognition_result(error_text)
                logging.info(f"修正测试: '{error_text}' -> '{corrected}'")
                
                if "of Blender" in corrected:
                    logging.info("✓ 成功修正为 'of Blender'")
                else:
                    logging.warning("✗ 修正失败")
            
            # 3. 综合评估
            logging.info("\n=== 综合评估 ===")
            
            success_criteria = [
                similarity > 0.7,  # 文本相似度超过70%
                word_accuracy > 75,  # 关键词准确率超过75%
                "lighting" in recognized_text.lower(),  # 必须包含核心词汇
                "important" in recognized_text.lower(),
                "aspect" in recognized_text.lower()
            ]
            
            passed_criteria = sum(success_criteria)
            total_criteria = len(success_criteria)
            
            logging.info(f"通过标准: {passed_criteria}/{total_criteria}")
            
            if passed_criteria >= 4:  # 至少通过4个标准
                logging.info("✓ 语音识别改进测试成功！")
                return True
            else:
                logging.warning("✗ 语音识别改进测试部分成功，仍需优化")
                return False
                
        else:
            logging.error("✗ 语音识别完全失败")
            return False
            
    except Exception as e:
        logging.error(f"✗ 测试过程中发生异常: {e}")
        return False

def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本的相似度
    
    :param text1: 文本1
    :param text2: 文本2
    :return: 相似度分数 (0-1)
    """
    if not text1 or not text2:
        return 0.0
    
    # 简单的词汇重叠度计算
    words1 = set(text1.split())
    words2 = set(text2.split())
    
    if not words1 or not words2:
        return 0.0
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union) if union else 0.0

def test_audio_preprocessing():
    """测试音频预处理功能"""
    
    logging.info("\n=== 测试音频预处理功能 ===")
    
    # 查找一个测试音频文件
    test_files = []
    if os.path.exists("temp_output"):
        for root, dirs, files in os.walk("temp_output"):
            for file in files:
                if file.endswith(".wav") and "semantic_merged" in file:
                    test_files.append(os.path.join(root, file))
                    if len(test_files) >= 3:  # 只测试3个文件
                        break
            if len(test_files) >= 3:
                break
    
    if not test_files:
        logging.warning("没有找到测试音频文件")
        return
    
    from api_services import preprocess_audio_for_asr
    
    for test_file in test_files:
        logging.info(f"测试预处理: {os.path.basename(test_file)}")
        
        try:
            processed_file = preprocess_audio_for_asr(test_file)
            
            if processed_file:
                if processed_file != test_file:
                    logging.info("✓ 音频已预处理并标准化")
                    # 清理临时文件
                    if os.path.exists(processed_file):
                        os.remove(processed_file)
                else:
                    logging.info("✓ 音频格式已符合要求，无需处理")
            else:
                logging.error("✗ 音频预处理失败")
                
        except Exception as e:
            logging.error(f"✗ 预处理异常: {e}")

if __name__ == "__main__":
    success = test_improved_asr()
    
    # 额外测试音频预处理
    test_audio_preprocessing()
    
    if success:
        print("\n🎉 改进后的语音识别测试成功！")
        print("✅ 音频预处理优化")
        print("✅ 识别参数调优")
        print("✅ 后处理错误修正")
        print("✅ 关键词识别准确")
    else:
        print("\n⚠️ 语音识别仍需进一步优化")
        print("建议检查音频质量和网络连接")
