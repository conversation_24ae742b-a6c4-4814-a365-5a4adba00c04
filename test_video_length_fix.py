#!/usr/bin/env python3
"""
测试视频长度修复功能
"""
import os
import ffmpeg
import logging
from video_processor import replace_audio

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_video_length_fix():
    """测试视频长度修复功能"""
    
    # 使用现有的文件进行测试
    video_path = "01. Installing and Opening Blender for the First Time Mammoth.mp4"
    
    # 使用最近生成的音频文件
    temp_dir = "temp_output/dubbing_20250802-202206"
    audio_path = os.path.join(temp_dir, "final_audio_buffered.wav")
    
    if not os.path.exists(video_path):
        logging.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not os.path.exists(audio_path):
        logging.error(f"测试音频文件不存在: {audio_path}")
        return False
    
    # 输出路径
    output_path = "test_video_length_fixed_v2.mp4"
    
    logging.info("开始测试视频长度修复...")
    logging.info(f"输入视频: {video_path}")
    logging.info(f"输入音频: {audio_path}")
    logging.info(f"输出视频: {output_path}")
    
    # 先检查原始文件的时长
    try:
        video_probe = ffmpeg.probe(video_path)
        audio_probe = ffmpeg.probe(audio_path)
        
        video_duration = float(video_probe['format']['duration'])
        audio_duration = float(audio_probe['format']['duration'])
        
        logging.info(f"原视频时长: {video_duration:.2f}秒")
        logging.info(f"音频时长: {audio_duration:.2f}秒")
        logging.info(f"时长差异: {audio_duration - video_duration:.2f}秒")
        
    except Exception as e:
        logging.error(f"获取文件信息失败: {e}")
        return False
    
    # 测试视频音轨替换
    try:
        success = replace_audio(video_path, audio_path, output_path)
        
        if success and os.path.exists(output_path):
            # 检查输出视频的时长
            output_probe = ffmpeg.probe(output_path)
            output_duration = float(output_probe['format']['duration'])
            
            logging.info(f"✓ 视频处理成功！")
            logging.info(f"输出视频时长: {output_duration:.2f}秒")
            logging.info(f"音频时长: {audio_duration:.2f}秒")
            
            # 验证输出视频时长是否足够容纳音频
            if output_duration >= audio_duration - 0.1:  # 允许0.1秒的误差
                logging.info("✓ 视频长度修复成功！输出视频能够完整播放音频")
                return True
            else:
                logging.error(f"✗ 视频长度修复失败！输出视频时长不足，缺少 {audio_duration - output_duration:.2f}秒")
                return False
        else:
            logging.error("✗ 视频处理失败")
            return False
            
    except Exception as e:
        logging.error(f"✗ 视频处理失败，发生异常: {e}")
        return False

if __name__ == "__main__":
    success = test_video_length_fix()
    if success:
        print("\n🎉 视频长度修复测试成功！")
    else:
        print("\n❌ 视频长度修复测试失败")
