# 火山语音合成API开发文档

## 目录
1. [概述](#概述)
2. [API接口说明](#api接口说明)
3. [语言支持](#语言支持)
4. [发音人配置](#发音人配置)
5. [核心实现](#核心实现)
6. [缓存机制](#缓存机制)
7. [错误处理](#错误处理)
8. [性能优化](#性能优化)
9. [集成指南](#集成指南)
10. [最佳实践](#最佳实践)

---

## 概述

火山语音合成（Volcano TTS）是字节跳动提供的文本转语音服务，支持多语言、多发音人的高质量语音合成。本文档基于TTime项目的实际应用经验，详细介绍了火山语音合成API的集成和使用方法。

### 主要特性
- 🌍 **多语言支持**: 支持中文、英语、日语、韩语等17种语言
- 🎭 **丰富发音人**: 提供100+种不同风格的发音人选择
- 🚀 **高性能**: 响应速度快，音质优秀
- 💰 **免费使用**: 无需API Key，公开接口
- 🔄 **智能缓存**: 内置缓存机制，避免重复请求

---

## API接口说明

### 基本信息
```
端点: https://translate.volcengine.com/crx/tts/v1/
方法: POST
认证: 无需API Key
内容类型: application/json
```

### 请求格式
```typescript
interface VolcanoTTSRequest {
  text: string;      // 要合成的文本（建议≤200字符）
  speaker: string;   // 发音人ID
  language: string;  // 语言代码
}
```

### 响应格式
```typescript
interface VolcanoTTSResponse {
  base_resp: {
    status_code: number;      // 0=成功，非0=失败
    status_message?: string;  // 错误信息
  };
  audio: {
    data: string;            // Base64编码的音频数据
    mimeType?: string;       // 音频格式，默认'audio/mpeg'
  };
}
```

### 请求示例
```javascript
const requestBody = {
  text: "你好，世界！",
  speaker: "tts.other.BV406_streaming",
  language: "zh"
};

const response = await fetch('https://translate.volcengine.com/crx/tts/v1/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Cache-Control': 'no-cache'
  },
  body: JSON.stringify(requestBody)
});
```

---

## 语言支持

### 支持的语言列表
| 语言 | 语言代码 | 默认发音人 | 发音人数量 |
|------|----------|------------|------------|
| 中文（简体） | zh | tts.other.BV406_streaming | 30+ |
| 中文（繁体） | zh_tw | tts.other.BV025_streaming | 10+ |
| 中文（粤语） | zh_yue | tts.other.BV026_streaming | 1 |
| 英语 | en | en_male_adam | 15+ |
| 日语 | ja | jp_male_satoshi | 8+ |
| 韩语 | ko | kr_male_gye | 2+ |
| 法语 | fr | fr_male_enzo | 2+ |
| 西班牙语 | es | es_male_george | 2+ |
| 俄语 | ru | tts.other.BV068_streaming | 1 |
| 德语 | de | de_female_sophie | 1 |
| 意大利语 | it | tts.other.BV087_streaming | 1 |
| 土耳其语 | tr | tts.other.BV083_streaming | 1 |
| 葡萄牙语 | pt | tts.other.BV531_streaming | 2+ |
| 越南语 | vi | tts.other.BV075_streaming | 2+ |
| 马来语 | ms | tts.other.BV092_streaming | 1 |
| 阿拉伯语 | ar | tts.other.BV570_streaming | 1 |
| 印尼语 | id | tts.other.BV160_streaming | 2+ |

### 语言代码映射函数
```typescript
const getVolcanoLanguage = (languageCode: string): string => {
  const languageMap: { [key: string]: string } = {
    // 中文系列
    'cmn': 'zh', 'zh': 'zh', 'zh-CN': 'zh',
    'yue': 'zh_tw', 'zh-TW': 'zh_tw', 'zh_tw': 'zh_tw',
    'zh_yue': 'zh_yue',
    
    // 英语
    'eng': 'en', 'en': 'en', 'en-US': 'en',
    
    // 日语
    'jpn': 'ja', 'ja': 'ja',
    
    // 韩语
    'kor': 'ko', 'ko': 'ko',
    
    // 法语
    'fra': 'fr', 'fr': 'fr',
    
    // 西班牙语
    'spa': 'es', 'es': 'es',
    
    // 俄语
    'rus': 'ru', 'ru': 'ru',
    
    // 德语
    'deu': 'de', 'de': 'de',
    
    // 意大利语
    'ita': 'it', 'it': 'it',
    
    // 土耳其语
    'tur': 'tr', 'tr': 'tr',
    
    // 葡萄牙语
    'por': 'pt', 'pt': 'pt',
    
    // 越南语
    'vie': 'vi', 'vi': 'vi',
    
    // 马来语
    'msa': 'ms', 'ms': 'ms', 'mal': 'ms', 'zlm': 'ms',
    
    // 阿拉伯语
    'ara': 'ar', 'ar': 'ar',
    
    // 印尼语
    'ind': 'id', 'id': 'id'
  };
  
  return languageMap[languageCode] || 'zh';
};
```

---

## 发音人配置

### 默认发音人配置
```typescript
const defaultSpeakers: { [key: string]: string } = {
  zh: 'tts.other.BV406_streaming',      // 超自然音色-梓梓
  zh_tw: 'tts.other.BV025_streaming',   // 台湾女声
  zh_yue: 'tts.other.BV026_streaming',  // 粤语男声
  en: 'en_male_adam',                   // 美式男声
  ja: 'jp_male_satoshi',                // 日语男声
  ko: 'kr_male_gye',                    // 韩语男声
  fr: 'fr_male_enzo',                   // 法语男声
  es: 'es_male_george',                 // 西语男声
  ru: 'tts.other.BV068_streaming',      // 俄语女声
  de: 'de_female_sophie',               // 德语女声
  it: 'tts.other.BV087_streaming',      // 意语男声
  tr: 'tts.other.BV083_streaming',      // 土耳其男声
  pt: 'tts.other.BV531_streaming',      // 葡语男声
  vi: 'tts.other.BV075_streaming',      // 越南男声
  ms: 'tts.other.BV092_streaming',      // 马来女声
  ar: 'tts.other.BV570_streaming',      // 阿语男声
  id: 'tts.other.BV160_streaming'       // 印尼男声
};
```

### 中文发音人选项（精选）
```typescript
const chineseSpeakers = [
  // 特色发音人
  { value: "tts.other.BV406_streaming", label: "超自然音色-梓梓" },
  { value: "tts.other.BV407_streaming", label: "超自然音色-燃燃" },
  { value: "zh_male_rap", label: "嘻哈歌手" },
  
  // 地方特色
  { value: "zh_female_sichuan", label: "四川女声" },
  { value: "tts.other.BV021_streaming", label: "东北男声" },
  { value: "tts.other.BV025_streaming", label: "台湾女声" },
  { value: "tts.other.BV026_streaming", label: "粤语男声" },
  
  // 专业配音
  { value: "zh_male_xiaoming", label: "影视配音" },
  { value: "zh_female_zhubo", label: "女主播" },
  { value: "zh_female_qingxin", label: "清新女声" },
  { value: "zh_female_story", label: "少儿故事" },
  
  // 情感特色
  { value: "zh_male_inspirational", label: "鸡汤哥哥" },
  { value: "zh_female_inspirational", label: "鸡汤妹妹" },
  { value: "tts.other.BV064_streaming", label: "小萝莉" },
  { value: "tts.other.BV405_streaming", label: "甜美小源" }
];
```

### 英语发音人选项
```typescript
const englishSpeakers = [
  // 美式发音
  { value: "en_male_adam", label: "美式男声" },
  { value: "tts.other.BV027_streaming", label: "美式女声-Amelia" },
  
  // 英式发音
  { value: "en_male_bob", label: "英式男声" },
  { value: "tts.other.BV032_TOBI_streaming", label: "英式女声" },
  { value: "en_male_smith", label: "英式男声-Smith" },
  
  // 澳洲发音
  { value: "tts.other.BV516_streaming", label: "澳洲男声" },
  { value: "en_female_sarah", label: "澳洲女声" },
  
  // 特色发音
  { value: "tts.other.BV511_streaming", label: "慵懒女声-Ava" },
  { value: "tts.other.BV502_streaming", label: "讲述女声-Amanda" },
  { value: "tts.other.BV503_streaming", label: "活力女声-Ariana" },
  { value: "tts.other.BV504_streaming", label: "活力男声-Jackson" }
];
```

### 发音人获取函数
```typescript
const getVolcanoSpeaker = (languageCode: string): string => {
  const language = getVolcanoLanguage(languageCode);
  
  // 从用户配置中获取发音人（如果有的话）
  const userSpeakerConfig = getUserSpeakerConfig();
  if (userSpeakerConfig && userSpeakerConfig[language]) {
    return userSpeakerConfig[language];
  }
  
  // 使用默认发音人
  return defaultSpeakers[language] || defaultSpeakers.zh;
};
```

---

## 核心实现

### 主要调用函数
```typescript
/**
 * 调用火山语音合成API
 * @param text 要合成的文本
 * @param languageCode 语言代码
 * @returns Promise<void>
 */
const callVolcanoTTS = async (text: string, languageCode: string): Promise<void> => {
  try {
    // 停止当前播放的音频
    stopCurrentAudio();

    // 文本长度优化：超长文本截取前200个字符
    const maxLength = 200;
    const optimizedText = text.length > maxLength ?
      text.substring(0, maxLength) + '...' : text;

    // 根据语言代码确定语言和发音人
    const language = getVolcanoLanguage(languageCode);
    const speaker = getVolcanoSpeaker(languageCode);

    // 生成缓存键
    const cacheKey = `${optimizedText}-${language}-${speaker}`;

    // 检查缓存
    if (audioCache.has(cacheKey)) {
      console.log('使用缓存音频');
      playAudioFromData(audioCache.get(cacheKey)!);
      return;
    }

    // 构建请求体
    const requestBody = {
      text: optimizedText,
      speaker: speaker,
      language: language
    };

    console.log('火山语音请求开始:', requestBody);
    const startTime = Date.now();

    // 发送API请求
    const response = await fetch('https://translate.volcengine.com/crx/tts/v1/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(requestBody)
    });

    const networkTime = Date.now() - startTime;
    console.log('网络请求耗时:', networkTime + 'ms');

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // 检查响应结果
    if (result.base_resp && result.base_resp.status_code === 0 &&
        result.audio && result.audio.data) {

      // 存储到缓存
      manageCacheSize(cacheKey, result);

      // 播放音频
      playAudioFromData(result);
    } else {
      const errorMessage = result.base_resp?.status_message || '未知错误';
      console.error('火山语音服务返回错误:', errorMessage);
      throw new Error('语音服务: ' + errorMessage);
    }
  } catch (error) {
    console.error('火山语音服务调用失败:', error);
    throw new Error(handleTTSError(error));
  }
};
```

### 音频播放函数
```typescript
/**
 * 播放火山语音返回的Base64音频数据
 * @param audioDataObj 音频数据对象
 */
const playAudioFromData = (audioDataObj: { audio: { data: string; mimeType?: string } }): void => {
  const { data, mimeType = 'audio/mpeg' } = audioDataObj.audio;

  if (!data) {
    throw new Error('音频数据为空');
  }

  const audioStartTime = Date.now();

  // 创建新的音频对象
  currentAudio = new Audio();
  const audioUrl = `data:${mimeType};base64,${data}`;

  // 设置预加载策略，加快播放速度
  currentAudio.preload = 'auto';
  currentAudio.src = audioUrl;

  // 错误处理
  currentAudio.onerror = (event: Event): void => {
    // 只记录真正的错误，忽略正常的停止操作
    if (currentAudio && currentAudio.src && currentAudio.src !== '') {
      console.error('音频播放错误:', event);
    }
  };

  // 当音频可以播放时开始播放
  currentAudio.oncanplay = (): void => {
    const playStartTime = Date.now();
    const totalTime = playStartTime - audioStartTime;
    console.log('音频准备完成，总耗时:', totalTime + 'ms');

    currentAudio!.play().catch((error: Error) => {
      console.error('音频播放失败:', error);
      // 只有在真正播放失败时才显示错误，忽略用户主动停止的情况
      if (error.name !== 'AbortError' && error.name !== 'NotAllowedError') {
        throw new Error('语音播放失败: ' + error.message);
      }
    });
  };

  // 音频播放结束时的清理
  currentAudio.onended = (): void => {
    URL.revokeObjectURL(audioUrl);
    currentAudio = null;
  };
};

/**
 * 停止当前播放的音频
 */
const stopCurrentAudio = (): void => {
  if (currentAudio) {
    currentAudio.pause();
    currentAudio.src = '';
    currentAudio = null;
  }
};

// 全局音频对象，确保单例播放
let currentAudio: HTMLAudioElement | null = null;
```

---

## 缓存机制

### 缓存实现
```typescript
// 音频缓存Map，键为缓存键，值为API响应数据
const audioCache = new Map<string, { audio: { data: string; mimeType?: string } }>();

/**
 * 生成缓存键
 * @param text 文本内容
 * @param language 语言代码
 * @param speaker 发音人ID
 * @returns 缓存键
 */
const generateCacheKey = (text: string, language: string, speaker: string): string => {
  return `${text}-${language}-${speaker}`;
};

/**
 * 管理缓存大小（LRU策略）
 * @param cacheKey 缓存键
 * @param result API响应结果
 */
const manageCacheSize = (cacheKey: string, result: any): void => {
  const maxCacheSize = 10;

  if (audioCache.size < maxCacheSize) {
    audioCache.set(cacheKey, result);
  } else {
    // 清理最旧的缓存项（LRU策略）
    const firstKey = audioCache.keys().next().value;
    audioCache.delete(firstKey);
    audioCache.set(cacheKey, result);
  }
};

/**
 * 清空缓存
 */
const clearAudioCache = (): void => {
  audioCache.clear();
  console.log('音频缓存已清空');
};

/**
 * 获取缓存统计信息
 */
const getCacheStats = (): { size: number; keys: string[] } => {
  return {
    size: audioCache.size,
    keys: Array.from(audioCache.keys())
  };
};
```

### 缓存策略说明
- **缓存键规则**: `文本内容-语言代码-发音人ID`
- **缓存大小限制**: 最多缓存10个音频文件
- **清理策略**: LRU（最近最少使用）算法
- **缓存命中**: 相同文本、语言、发音人的请求直接使用缓存
- **内存管理**: 自动清理最旧的缓存项，防止内存溢出

---

## 错误处理

### 错误类型和处理策略
```typescript
/**
 * 处理TTS错误
 * @param error 错误对象
 * @returns 用户友好的错误信息
 */
const handleTTSError = (error: any): string => {
  let errorMessage = '火山语音服务调用失败';

  if (error.message.includes('fetch') || error.message.includes('network')) {
    errorMessage = '网络连接失败，请检查网络连接';
  } else if (error.message.includes('timeout')) {
    errorMessage = '网络请求超时，请稍后重试';
  } else if (error.message.includes('HTTP error')) {
    const status = error.message.match(/status: (\d+)/)?.[1];
    switch (status) {
      case '400':
        errorMessage = '请求参数错误，请检查文本内容';
        break;
      case '429':
        errorMessage = '请求过于频繁，请稍后重试';
        break;
      case '500':
        errorMessage = '服务器内部错误，请稍后重试';
        break;
      default:
        errorMessage = `服务器响应错误(${status})，请稍后重试`;
    }
  } else if (error.message.includes('语音服务:')) {
    errorMessage = error.message; // API返回的具体错误信息
  } else {
    errorMessage = '火山语音服务调用失败: ' + error.message;
  }

  return errorMessage;
};

/**
 * 错误重试机制
 * @param fn 要重试的函数
 * @param maxRetries 最大重试次数
 * @param delay 重试延迟（毫秒）
 */
const retryWithDelay = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      if (i === maxRetries) {
        throw lastError;
      }

      console.log(`重试第${i + 1}次，${delay}ms后重试...`);
      await new Promise(resolve => setTimeout(resolve, delay));

      // 指数退避策略
      delay *= 2;
    }
  }

  throw lastError!;
};
```

### 常见错误码说明
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 0 | 成功 | - |
| 400 | 请求参数错误 | 检查文本内容、发音人ID、语言代码 |
| 429 | 请求频率过高 | 降低请求频率，增加延迟 |
| 500 | 服务器内部错误 | 稍后重试 |
| 网络错误 | 网络连接问题 | 检查网络连接 |
| 超时错误 | 请求超时 | 增加超时时间或重试 |

---

## 性能优化

### 优化策略
```typescript
/**
 * 性能优化配置
 */
const performanceConfig = {
  // 文本长度限制
  maxTextLength: 200,

  // 缓存配置
  maxCacheSize: 10,
  cacheEnabled: true,

  // 网络配置
  requestTimeout: 10000, // 10秒超时
  maxRetries: 3,
  retryDelay: 1000,

  // 音频配置
  preloadStrategy: 'auto', // 预加载策略
  audioFormat: 'audio/mpeg'
};

/**
 * 文本预处理
 * @param text 原始文本
 * @returns 处理后的文本
 */
const preprocessText = (text: string): string => {
  // 移除多余的空白字符
  let processedText = text.trim().replace(/\s+/g, ' ');

  // 长度限制
  if (processedText.length > performanceConfig.maxTextLength) {
    processedText = processedText.substring(0, performanceConfig.maxTextLength) + '...';
    console.log(`文本过长，已截取至${performanceConfig.maxTextLength}字符`);
  }

  // 移除特殊字符（可选）
  // processedText = processedText.replace(/[^\w\s\u4e00-\u9fff]/g, '');

  return processedText;
};

/**
 * 请求防抖
 */
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  };
};

// 防抖的TTS调用函数
const debouncedCallVolcanoTTS = debounce(callVolcanoTTS, 300);
```

### 性能监控
```typescript
/**
 * 性能监控
 */
class TTSPerformanceMonitor {
  private metrics: {
    totalRequests: number;
    successRequests: number;
    failedRequests: number;
    cacheHits: number;
    averageResponseTime: number;
    responseTimeHistory: number[];
  } = {
    totalRequests: 0,
    successRequests: 0,
    failedRequests: 0,
    cacheHits: 0,
    averageResponseTime: 0,
    responseTimeHistory: []
  };

  recordRequest(responseTime: number, fromCache: boolean = false): void {
    this.metrics.totalRequests++;

    if (fromCache) {
      this.metrics.cacheHits++;
    } else {
      this.metrics.responseTimeHistory.push(responseTime);
      this.updateAverageResponseTime();
    }

    this.metrics.successRequests++;
  }

  recordError(): void {
    this.metrics.totalRequests++;
    this.metrics.failedRequests++;
  }

  private updateAverageResponseTime(): void {
    const history = this.metrics.responseTimeHistory;
    if (history.length > 0) {
      this.metrics.averageResponseTime =
        history.reduce((sum, time) => sum + time, 0) / history.length;
    }

    // 保持历史记录在合理范围内
    if (history.length > 100) {
      this.metrics.responseTimeHistory = history.slice(-50);
    }
  }

  getMetrics() {
    return {
      ...this.metrics,
      successRate: this.metrics.totalRequests > 0 ?
        (this.metrics.successRequests / this.metrics.totalRequests * 100).toFixed(2) + '%' : '0%',
      cacheHitRate: this.metrics.totalRequests > 0 ?
        (this.metrics.cacheHits / this.metrics.totalRequests * 100).toFixed(2) + '%' : '0%'
    };
  }

  reset(): void {
    this.metrics = {
      totalRequests: 0,
      successRequests: 0,
      failedRequests: 0,
      cacheHits: 0,
      averageResponseTime: 0,
      responseTimeHistory: []
    };
  }
}

const performanceMonitor = new TTSPerformanceMonitor();
```

---

## 集成指南

### 快速开始
```typescript
// 1. 复制核心函数到你的项目
import { callVolcanoTTS, getVolcanoLanguage, getVolcanoSpeaker } from './volcano-tts';

// 2. 基本使用
async function speakText(text: string, language: string = 'zh') {
  try {
    await callVolcanoTTS(text, language);
    console.log('语音播放成功');
  } catch (error) {
    console.error('语音播放失败:', error.message);
  }
}

// 3. 使用示例
speakText('Hello, World!', 'en');
speakText('你好，世界！', 'zh');
speakText('こんにちは、世界！', 'ja');
```

### 完整集成示例
```typescript
/**
 * 火山TTS服务类
 */
class VolcanoTTSService {
  private audioCache = new Map();
  private currentAudio: HTMLAudioElement | null = null;
  private performanceMonitor = new TTSPerformanceMonitor();

  constructor(private config = {
    maxTextLength: 200,
    maxCacheSize: 10,
    requestTimeout: 10000
  }) {}

  /**
   * 语音合成
   * @param text 文本内容
   * @param options 选项
   */
  async speak(text: string, options: {
    language?: string;
    speaker?: string;
    useCache?: boolean;
  } = {}): Promise<void> {
    const {
      language = 'zh',
      speaker,
      useCache = true
    } = options;

    try {
      // 预处理文本
      const processedText = this.preprocessText(text);

      // 获取语言和发音人
      const volcanoLanguage = getVolcanoLanguage(language);
      const volcanoSpeaker = speaker || getVolcanoSpeaker(language);

      // 生成缓存键
      const cacheKey = `${processedText}-${volcanoLanguage}-${volcanoSpeaker}`;

      // 检查缓存
      if (useCache && this.audioCache.has(cacheKey)) {
        console.log('使用缓存音频');
        this.performanceMonitor.recordRequest(0, true);
        this.playAudioFromData(this.audioCache.get(cacheKey));
        return;
      }

      // 调用API
      const startTime = Date.now();
      const result = await this.callAPI(processedText, volcanoLanguage, volcanoSpeaker);
      const responseTime = Date.now() - startTime;

      // 记录性能指标
      this.performanceMonitor.recordRequest(responseTime, false);

      // 缓存结果
      if (useCache) {
        this.manageCacheSize(cacheKey, result);
      }

      // 播放音频
      this.playAudioFromData(result);

    } catch (error) {
      this.performanceMonitor.recordError();
      throw error;
    }
  }

  /**
   * 停止播放
   */
  stop(): void {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.src = '';
      this.currentAudio = null;
    }
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    return this.performanceMonitor.getMetrics();
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.audioCache.clear();
  }

  // ... 其他私有方法
}

// 使用示例
const ttsService = new VolcanoTTSService();

// 基本使用
await ttsService.speak('你好，世界！');

// 指定语言和发音人
await ttsService.speak('Hello, World!', {
  language: 'en',
  speaker: 'en_male_adam'
});

// 获取性能指标
console.log(ttsService.getPerformanceMetrics());
```

### React集成示例
```tsx
import React, { useState, useCallback } from 'react';
import { VolcanoTTSService } from './volcano-tts-service';

const TTSComponent: React.FC = () => {
  const [ttsService] = useState(() => new VolcanoTTSService());
  const [isPlaying, setIsPlaying] = useState(false);
  const [text, setText] = useState('');
  const [language, setLanguage] = useState('zh');

  const handleSpeak = useCallback(async () => {
    if (!text.trim()) return;

    try {
      setIsPlaying(true);
      await ttsService.speak(text, { language });
    } catch (error) {
      console.error('语音播放失败:', error);
      alert('语音播放失败: ' + error.message);
    } finally {
      setIsPlaying(false);
    }
  }, [text, language, ttsService]);

  const handleStop = useCallback(() => {
    ttsService.stop();
    setIsPlaying(false);
  }, [ttsService]);

  return (
    <div className="tts-component">
      <textarea
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="请输入要朗读的文本..."
        rows={4}
        cols={50}
      />

      <div>
        <select value={language} onChange={(e) => setLanguage(e.target.value)}>
          <option value="zh">中文</option>
          <option value="en">英语</option>
          <option value="ja">日语</option>
          <option value="ko">韩语</option>
        </select>
      </div>

      <div>
        <button onClick={handleSpeak} disabled={isPlaying || !text.trim()}>
          {isPlaying ? '播放中...' : '开始朗读'}
        </button>
        <button onClick={handleStop} disabled={!isPlaying}>
          停止播放
        </button>
      </div>
    </div>
  );
};

export default TTSComponent;
```

### Vue集成示例
```vue
<template>
  <div class="tts-component">
    <textarea
      v-model="text"
      placeholder="请输入要朗读的文本..."
      rows="4"
      cols="50"
    />

    <div>
      <select v-model="language">
        <option value="zh">中文</option>
        <option value="en">英语</option>
        <option value="ja">日语</option>
        <option value="ko">韩语</option>
      </select>
    </div>

    <div>
      <button @click="handleSpeak" :disabled="isPlaying || !text.trim()">
        {{ isPlaying ? '播放中...' : '开始朗读' }}
      </button>
      <button @click="handleStop" :disabled="!isPlaying">
        停止播放
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { VolcanoTTSService } from './volcano-tts-service';

const text = ref('');
const language = ref('zh');
const isPlaying = ref(false);
let ttsService: VolcanoTTSService;

onMounted(() => {
  ttsService = new VolcanoTTSService();
});

onUnmounted(() => {
  if (ttsService) {
    ttsService.stop();
  }
});

const handleSpeak = async () => {
  if (!text.value.trim()) return;

  try {
    isPlaying.value = true;
    await ttsService.speak(text.value, { language: language.value });
  } catch (error) {
    console.error('语音播放失败:', error);
    alert('语音播放失败: ' + error.message);
  } finally {
    isPlaying.value = false;
  }
};

const handleStop = () => {
  ttsService.stop();
  isPlaying.value = false;
};
</script>
```

---

## 最佳实践

### 1. 文本处理最佳实践
```typescript
/**
 * 文本预处理最佳实践
 */
const textProcessingBestPractices = {
  // 长度控制
  maxLength: 200,

  // 文本清理
  cleanText: (text: string): string => {
    return text
      .trim()                           // 移除首尾空白
      .replace(/\s+/g, ' ')            // 合并多个空格
      .replace(/[^\w\s\u4e00-\u9fff.,!?;:]/g, '') // 保留基本标点
      .substring(0, 200);              // 长度限制
  },

  // 分段处理长文本
  splitLongText: (text: string, maxLength: number = 200): string[] => {
    if (text.length <= maxLength) return [text];

    const sentences = text.split(/[.!?。！？]/);
    const chunks: string[] = [];
    let currentChunk = '';

    for (const sentence of sentences) {
      if ((currentChunk + sentence).length <= maxLength) {
        currentChunk += sentence;
      } else {
        if (currentChunk) chunks.push(currentChunk.trim());
        currentChunk = sentence;
      }
    }

    if (currentChunk) chunks.push(currentChunk.trim());
    return chunks.filter(chunk => chunk.length > 0);
  }
};
```

### 2. 缓存策略最佳实践
```typescript
/**
 * 缓存策略最佳实践
 */
class SmartCache {
  private cache = new Map();
  private accessTimes = new Map();
  private maxSize: number;

  constructor(maxSize: number = 10) {
    this.maxSize = maxSize;
  }

  set(key: string, value: any): void {
    // 如果缓存已满，清理最少使用的项
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLeastUsed();
    }

    this.cache.set(key, value);
    this.accessTimes.set(key, Date.now());
  }

  get(key: string): any {
    if (this.cache.has(key)) {
      this.accessTimes.set(key, Date.now()); // 更新访问时间
      return this.cache.get(key);
    }
    return null;
  }

  private evictLeastUsed(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.accessTimes.delete(oldestKey);
    }
  }

  clear(): void {
    this.cache.clear();
    this.accessTimes.clear();
  }

  size(): number {
    return this.cache.size;
  }
}
```

### 3. 用户体验最佳实践
```typescript
/**
 * 用户体验最佳实践
 */
const uxBestPractices = {
  // 播放状态管理
  createPlaybackManager: () => {
    let isPlaying = false;
    let currentText = '';
    const listeners: Array<(state: any) => void> = [];

    return {
      setPlaying: (playing: boolean, text?: string) => {
        isPlaying = playing;
        if (text) currentText = text;
        listeners.forEach(listener => listener({ isPlaying, currentText }));
      },

      getState: () => ({ isPlaying, currentText }),

      subscribe: (listener: (state: any) => void) => {
        listeners.push(listener);
        return () => {
          const index = listeners.indexOf(listener);
          if (index > -1) listeners.splice(index, 1);
        };
      }
    };
  },

  // 键盘快捷键支持
  setupKeyboardShortcuts: (ttsService: VolcanoTTSService) => {
    document.addEventListener('keydown', (event) => {
      // Ctrl/Cmd + Shift + S: 朗读选中文本
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        const selectedText = window.getSelection()?.toString();
        if (selectedText) {
          ttsService.speak(selectedText);
        }
      }

      // Esc: 停止播放
      if (event.key === 'Escape') {
        ttsService.stop();
      }
    });
  },

  // 无障碍支持
  setupAccessibility: (button: HTMLElement, ttsService: VolcanoTTSService) => {
    button.setAttribute('role', 'button');
    button.setAttribute('aria-label', '朗读文本');
    button.setAttribute('tabindex', '0');

    // 键盘支持
    button.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        button.click();
      }
    });

    // 状态更新
    const updateAriaLabel = (isPlaying: boolean) => {
      button.setAttribute('aria-label', isPlaying ? '停止朗读' : '开始朗读');
    };

    return { updateAriaLabel };
  }
};
```

---

## 总结

火山语音合成API是一个功能强大、易于集成的文本转语音服务。通过本文档提供的完整实现方案，您可以：

### ✅ 核心功能
- 支持17种语言的高质量语音合成
- 提供100+种不同风格的发音人选择
- 无需API Key，免费使用
- 响应速度快，音质优秀

### ✅ 技术特性
- 完整的错误处理和重试机制
- 智能缓存系统，提升性能
- 防抖和节流，优化用户体验
- 详细的性能监控和指标统计

### ✅ 集成优势
- 代码结构清晰，易于理解和维护
- 支持多种前端框架（React、Vue等）
- 提供完整的TypeScript类型定义
- 遵循最佳实践，保证代码质量

### 🚀 快速开始
1. 复制核心实现代码到您的项目
2. 根据需求调整配置参数
3. 选择合适的发音人和语言
4. 集成到您的用户界面
5. 添加错误处理和用户反馈

通过遵循本文档的指导，您可以快速在项目中集成高质量的语音合成功能，为用户提供优秀的语音体验。

---

**文档版本**: v1.0
**最后更新**: 2024年12月
**基于项目**: TTime翻译工具
**技术栈**: TypeScript, HTML5 Audio API, Fetch API
```
```
```
```
