#!/usr/bin/env python3
"""
测试基于语义的智能语音分段合并功能
"""
import os
import logging
from video_processor import split_audio_on_silence, smart_semantic_merge
from main_controller import get_audio_duration

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_semantic_merge():
    """测试语义合并功能"""
    
    # 使用现有的音频文件进行测试
    video_path = "01. Installing and Opening Blender for the First Time Mammoth.mp4"
    
    if not os.path.exists(video_path):
        logging.error(f"测试视频文件不存在: {video_path}")
        return False
    
    # 创建测试目录
    test_dir = "test_semantic_merge_output"
    os.makedirs(test_dir, exist_ok=True)
    
    logging.info("开始测试语义合并功能...")
    logging.info(f"输入视频: {video_path}")
    logging.info(f"测试目录: {test_dir}")
    
    try:
        # 1. 提取音频
        logging.info("=== 步骤1: 提取音频 ===")
        from video_processor import extract_audio
        
        audio_path = os.path.join(test_dir, "extracted_audio.wav")
        success = extract_audio(video_path, audio_path)
        
        if not success:
            logging.error("音频提取失败")
            return False
        
        audio_duration = get_audio_duration(audio_path)
        logging.info(f"提取的音频时长: {audio_duration:.2f}秒")
        
        # 2. 分离人声
        logging.info("=== 步骤2: 分离人声 ===")
        from video_processor import separate_vocals
        
        vocals_path, accompaniment_path = separate_vocals(audio_path, test_dir)
        
        if not vocals_path:
            logging.error("人声分离失败")
            return False
        
        vocals_duration = get_audio_duration(vocals_path)
        logging.info(f"分离的人声时长: {vocals_duration:.2f}秒")
        
        # 3. 音频分段（使用更严格的参数来产生更多短片段）
        logging.info("=== 步骤3: 音频分段 ===")
        
        chunks_dir = os.path.join(test_dir, "chunks")
        os.makedirs(chunks_dir, exist_ok=True)
        
        # 使用更严格的参数来产生短片段
        audio_chunks = split_audio_on_silence(
            vocals_path,
            chunks_dir,
            min_silence_len=200,      # 更短的静音检测
            silence_thresh=-40,       # 更敏感的静音阈值
            keep_silence=200,         # 更少的上下文
            max_chunk_duration_ms=8000  # 更短的最大片段长度
        )
        
        if not audio_chunks:
            logging.error("音频分段失败")
            return False
        
        logging.info(f"初始分段数量: {len(audio_chunks)}")
        
        # 4. 分析分段情况
        logging.info("=== 步骤4: 分析分段情况 ===")
        
        short_chunks = []
        total_duration = 0
        
        for i, chunk in enumerate(audio_chunks):
            duration = chunk['end_ms'] - chunk['start_ms']
            total_duration += duration
            
            logging.info(f"片段 {i+1}: {duration}ms ({duration/1000:.2f}s) - {os.path.basename(chunk['path'])}")
            
            if duration < 1500:
                short_chunks.append(i)
        
        logging.info(f"总分段数: {len(audio_chunks)}")
        logging.info(f"短片段数 (<1.5s): {len(short_chunks)}")
        logging.info(f"总时长: {total_duration/1000:.2f}秒")
        
        if len(short_chunks) == 0:
            logging.warning("没有发现短片段，无法测试语义合并功能")
            return True
        
        # 5. 测试语义合并
        logging.info("=== 步骤5: 测试语义合并 ===")
        
        # 先测试几个短片段的语音识别
        logging.info("测试短片段的语音识别...")
        for i in short_chunks[:3]:  # 只测试前3个短片段
            chunk = audio_chunks[i]
            duration = chunk['end_ms'] - chunk['start_ms']
            
            try:
                from video_processor import recognize_audio_text
                text = recognize_audio_text(chunk['path'])
                logging.info(f"片段 {i+1} ({duration}ms): '{text}'")
            except Exception as e:
                logging.warning(f"片段 {i+1} 语音识别失败: {e}")
        
        # 执行智能合并
        logging.info("执行智能语义合并...")
        merged_chunks = smart_semantic_merge(audio_chunks, min_duration_ms=1500)
        
        # 6. 分析合并结果
        logging.info("=== 步骤6: 分析合并结果 ===")
        
        logging.info(f"合并前片段数: {len(audio_chunks)}")
        logging.info(f"合并后片段数: {len(merged_chunks)}")
        logging.info(f"减少片段数: {len(audio_chunks) - len(merged_chunks)}")
        
        # 检查合并后的短片段数量
        merged_short_chunks = []
        for i, chunk in enumerate(merged_chunks):
            duration = chunk['end_ms'] - chunk['start_ms']
            if duration < 1500:
                merged_short_chunks.append(i)
        
        logging.info(f"合并后短片段数: {len(merged_short_chunks)}")
        
        # 显示合并后的片段信息
        for i, chunk in enumerate(merged_chunks):
            duration = chunk['end_ms'] - chunk['start_ms']
            logging.info(f"合并后片段 {i+1}: {duration}ms ({duration/1000:.2f}s) - {os.path.basename(chunk['path'])}")
        
        # 7. 验证结果
        if len(merged_chunks) <= len(audio_chunks) and len(merged_short_chunks) <= len(short_chunks):
            logging.info("✓ 语义合并功能测试成功！")
            return True
        else:
            logging.error("✗ 语义合并功能测试失败")
            return False
            
    except Exception as e:
        logging.error(f"✗ 测试失败，发生异常: {e}")
        return False

if __name__ == "__main__":
    success = test_semantic_merge()
    if success:
        print("\n🎉 语义合并功能测试成功！")
        print("✅ 短片段智能合并")
        print("✅ 语音识别集成")
        print("✅ 语义分析决策")
    else:
        print("\n❌ 语义合并功能测试失败")
