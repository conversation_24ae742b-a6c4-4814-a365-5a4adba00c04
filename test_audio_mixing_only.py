#!/usr/bin/env python3
"""
仅测试音频混合功能（使用现有的音频文件）
"""
import os
import ffmpeg
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_audio_mixing_with_existing_files():
    """使用现有的音频文件测试混合功能"""
    
    # 使用最近的临时目录中的文件
    temp_dir = "temp_output/dubbing_20250802-175244"
    
    if not os.path.exists(temp_dir):
        logging.error(f"临时目录不存在: {temp_dir}")
        return False
    
    dub_audio_path = os.path.join(temp_dir, "dub_only_audio.mp3")
    accompaniment_path = os.path.join(temp_dir, "accompaniment.wav")
    
    if not os.path.exists(dub_audio_path):
        logging.error(f"配音文件不存在: {dub_audio_path}")
        return False
        
    if not os.path.exists(accompaniment_path):
        logging.error(f"背景音文件不存在: {accompaniment_path}")
        return False
    
    # 测试新的混合方法
    output_path = os.path.join(temp_dir, "test_fixed_mixing.wav")
    
    logging.info("开始测试修复后的音频混合...")
    logging.info(f"配音文件: {dub_audio_path}")
    logging.info(f"背景音文件: {accompaniment_path}")
    logging.info(f"输出文件: {output_path}")
    
    try:
        # 使用与main_controller.py相同的方法
        # 标准化音频格式 - 统一采样率和格式
        input_dub = ffmpeg.input(dub_audio_path).audio.filter('aformat', sample_fmts='fltp', sample_rates='44100', channel_layouts='stereo')
        input_accompaniment = ffmpeg.input(accompaniment_path).audio.filter('aformat', sample_fmts='fltp', sample_rates='44100', channel_layouts='stereo').filter('volume', '0.5')
        
        # 使用简单的amix进行混合（在格式标准化后更稳定）
        mixed_stream = ffmpeg.filter([input_dub, input_accompaniment], 'amix', inputs=2, duration='first')
        
        # 输出为WAV格式
        ffmpeg.run(ffmpeg.output(mixed_stream, output_path, acodec='pcm_s16le'), overwrite_output=True, capture_stderr=True)
        
        if os.path.exists(output_path):
            logging.info(f"✓ 音频混合成功！输出文件: {output_path}")
            
            # 检查输出文件的属性
            probe = ffmpeg.probe(output_path)
            stream = probe['streams'][0]
            logging.info(f"输出音频信息:")
            logging.info(f"  编解码器: {stream.get('codec_name', 'unknown')}")
            logging.info(f"  采样率: {stream.get('sample_rate', 'unknown')}")
            logging.info(f"  声道数: {stream.get('channels', 'unknown')}")
            logging.info(f"  时长: {stream.get('duration', 'unknown')}")
            
            return True
        else:
            logging.error("✗ 音频混合失败：输出文件未生成")
            return False
            
    except ffmpeg.Error as e:
        logging.error(f"✗ 音频混合失败，FFmpeg错误详情:")
        logging.error(e.stderr.decode('utf8'))
        return False
    except Exception as e:
        logging.error(f"✗ 音频混合失败，发生异常: {e}")
        return False

if __name__ == "__main__":
    success = test_audio_mixing_with_existing_files()
    if success:
        print("\n🎉 音频混合修复测试成功！")
    else:
        print("\n❌ 音频混合修复测试失败")
