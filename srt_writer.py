'''
Author: mobolun <EMAIL>
Date: 2025-07-25 22:20:13
LastEditors: mobolun <EMAIL>
LastEditTime: 2025-07-25 22:20:30
FilePath: \VedioLocal\srt_writer.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
# -*- coding: utf-8 -*-

import logging
from datetime import timedelta

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def _format_time(time_ms: int) -> str:
    """将毫秒数转换为SRT时间格式 (HH:MM:SS,ms)"""
    td = timedelta(milliseconds=time_ms)
    total_seconds = int(td.total_seconds())
    ms = td.microseconds // 1000
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{ms:03d}"

def write_srt(subtitle_entries: list, output_path: str):
    """
    将字幕条目列表写入到 SRT 文件中。

    :param subtitle_entries: 一个包含字幕信息的字典列表。
                              每个字典应包含: 'start_ms', 'end_ms', 'source_text', 'target_text'
    :param output_path: 输出 SRT 文件的路径。
    """
    logging.info(f"开始将 {len(subtitle_entries)} 条字幕写入到: {output_path}")
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            for i, entry in enumerate(subtitle_entries):
                index = i + 1
                start_time = _format_time(entry['start_ms'])
                end_time = _format_time(entry['end_ms'])
                source_text = entry['source_text']
                target_text = entry['target_text']
                
                f.write(f"{index}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{target_text}\n") # 目标语言在上方
                f.write(f"{source_text}\n\n") # 源语言在下方

        logging.info(f"SRT 文件已成功创建: {output_path}")
        return True
    except Exception as e:
        logging.error(f"写入 SRT 文件时发生错误: {e}", exc_info=True)
        return False