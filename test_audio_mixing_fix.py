#!/usr/bin/env python3
"""
测试修复后的音频混合功能
"""
import os
import ffmpeg
import logging
from main_controller import get_audio_duration

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_audio_mixing_fix():
    """测试修复后的音频混合功能"""
    
    # 使用现有的音频文件
    temp_dir = "temp_output/dubbing_20250802-202206"
    
    if not os.path.exists(temp_dir):
        logging.error(f"临时目录不存在: {temp_dir}")
        return False
    
    dub_audio_path = os.path.join(temp_dir, "dub_only_audio.mp3")
    accompaniment_path = os.path.join(temp_dir, "accompaniment.wav")
    
    if not os.path.exists(dub_audio_path):
        logging.error(f"配音文件不存在: {dub_audio_path}")
        return False
        
    if not os.path.exists(accompaniment_path):
        logging.error(f"背景音文件不存在: {accompaniment_path}")
        return False
    
    # 输出路径
    final_audio_path = os.path.join(temp_dir, "test_final_mixed_audio_fixed.wav")
    final_audio_buffered_path = os.path.join(temp_dir, "test_final_audio_buffered_fixed.wav")
    
    logging.info("开始测试修复后的音频混合...")
    logging.info(f"配音文件: {dub_audio_path}")
    logging.info(f"背景音文件: {accompaniment_path}")
    
    try:
        # 获取配音音轨和背景音的时长
        dub_duration = get_audio_duration(dub_audio_path)
        accompaniment_duration = get_audio_duration(accompaniment_path)
        
        logging.info(f"配音音轨时长: {dub_duration:.2f}秒")
        logging.info(f"背景音时长: {accompaniment_duration:.2f}秒")

        input_dub = ffmpeg.input(dub_audio_path).audio
        
        # 如果背景音比配音短，需要延长背景音；如果背景音比配音长，需要截取背景音
        if accompaniment_duration < dub_duration:
            # 背景音比配音短，使用 apad 延长背景音
            logging.info(f"背景音比配音短 {dub_duration - accompaniment_duration:.2f}秒，将延长背景音")
            input_accompaniment = ffmpeg.input(accompaniment_path).audio.filter('apad', pad_dur=dub_duration - accompaniment_duration).filter('volume', '0.5')
        else:
            # 背景音比配音长或相等，截取背景音到配音长度
            logging.info(f"截取背景音到配音长度: {dub_duration:.2f}秒")
            input_accompaniment = ffmpeg.input(accompaniment_path).audio.filter('atrim', duration=dub_duration).filter('volume', '0.5')
        
        # 使用 amerge + pan 方案进行混合
        merged_stream = ffmpeg.filter([input_dub, input_accompaniment], 'amerge', inputs=2)
        mixed_stream = ffmpeg.filter(merged_stream, 'pan', 'stereo|c0<c0+c2|c1<c1+c3')

        # 应用音量调节
        volume = 1.0
        final_mixed_audio = mixed_stream.filter('volume', str(volume))

        ffmpeg.run(ffmpeg.output(final_mixed_audio, final_audio_path, acodec='pcm_s16le'), overwrite_output=True, capture_stderr=True)
        logging.info(f"最终混合音轨已合成: {final_audio_path}")
        
        # 检查生成的音频时长
        final_duration = get_audio_duration(final_audio_path)
        logging.info(f"生成的混合音频时长: {final_duration:.2f}秒")
        
        # 验证时长是否正确
        if abs(final_duration - dub_duration) < 0.1:  # 允许0.1秒误差
            logging.info("✓ 音频混合成功！时长正确")
            
            # 测试添加3秒缓冲
            logging.info("添加3秒缓冲...")
            three_second_silence_path = os.path.join(temp_dir, "test_silence_3s.wav")
            
            # 创建3秒静音
            ffmpeg.input('anullsrc', format='lavfi', t=3.0).output(three_second_silence_path, acodec='pcm_s16le', ar='44100').run(overwrite_output=True, capture_stderr=True)
            
            # 连接音频和静音
            input_main_audio = ffmpeg.input(final_audio_path)
            input_silence = ffmpeg.input(three_second_silence_path)
            concatenated_audio = ffmpeg.concat(input_main_audio, input_silence, v=0, a=1)
            ffmpeg.run(concatenated_audio.output(final_audio_buffered_path), overwrite_output=True, capture_stderr=True)
            
            buffered_duration = get_audio_duration(final_audio_buffered_path)
            logging.info(f"带缓冲的音频时长: {buffered_duration:.2f}秒")
            
            if abs(buffered_duration - (dub_duration + 3.0)) < 0.1:
                logging.info("✓ 缓冲添加成功！")
                return True
            else:
                logging.error(f"✗ 缓冲添加失败！期望时长: {dub_duration + 3.0:.2f}秒，实际时长: {buffered_duration:.2f}秒")
                return False
        else:
            logging.error(f"✗ 音频混合失败！期望时长: {dub_duration:.2f}秒，实际时长: {final_duration:.2f}秒")
            return False
            
    except Exception as e:
        logging.error(f"✗ 音频混合测试失败，发生异常: {e}")
        return False

if __name__ == "__main__":
    success = test_audio_mixing_fix()
    if success:
        print("\n🎉 音频混合修复测试成功！")
    else:
        print("\n❌ 音频混合修复测试失败")
