#!/usr/bin/env python3
"""
测试修复后的音频混合功能
"""
import os
import logging
from main_controller import process_video_with_srt

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_video_processing():
    """测试视频处理流程"""
    
    # 使用现有的测试文件
    video_path = "01. Installing and Opening Blender for the First Time Mammoth.mp4"
    srt_path = "01. Installing and Opening Blender for the First Time Mammoth.srt"
    
    if not os.path.exists(video_path):
        logging.error(f"测试视频文件不存在: {video_path}")
        return False
        
    if not os.path.exists(srt_path):
        logging.error(f"测试字幕文件不存在: {srt_path}")
        return False
    
    # 设置输出路径
    output_path = "test_output_fixed.mp4"
    
    # 测试参数
    from_lang = "en"
    to_lang = "zh"
    speaker = "zh_female_shuangkuaisisi_moon_bigtts"
    volume = 1.0
    provider = "niutrans"
    
    logging.info("开始测试修复后的视频处理流程...")
    logging.info(f"输入视频: {video_path}")
    logging.info(f"输入字幕: {srt_path}")
    logging.info(f"输出视频: {output_path}")
    logging.info(f"翻译: {from_lang} -> {to_lang}")
    logging.info(f"发音人: {speaker}")
    logging.info(f"音量: {volume}")
    logging.info(f"翻译服务: {provider}")
    
    try:
        process_video_with_srt(
            video_path=video_path,
            srt_path=srt_path,
            from_lang=from_lang,
            to_lang=to_lang,
            output_path=output_path,
            speaker=speaker,
            volume=volume,
            provider=provider
        )
        
        if os.path.exists(output_path):
            logging.info(f"✓ 测试成功！输出文件已生成: {output_path}")
            return True
        else:
            logging.error("✗ 测试失败：输出文件未生成")
            return False
            
    except Exception as e:
        logging.error(f"✗ 测试失败，发生异常: {e}")
        return False

if __name__ == "__main__":
    success = test_video_processing()
    if success:
        print("\n🎉 音频混合修复测试成功！")
    else:
        print("\n❌ 音频混合修复测试失败")
