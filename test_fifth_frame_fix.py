#!/usr/bin/env python3
"""
测试倒数第5帧修复方案，解决视频延长时的黑屏问题
"""
import os
import ffmpeg
import logging
from video_processor import replace_audio
from main_controller import get_audio_duration, create_silent_audio

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_fifth_frame_fix():
    """测试倒数第5帧修复方案"""
    
    # 使用现有的音频文件
    temp_dir = "temp_output/dubbing_20250802-232915"
    video_path = "01. Installing and Opening Blender for the First Time Mammoth.mp4"
    
    # 使用完整的配音文件
    dub_audio_path = os.path.join(temp_dir, "dub_only_audio.mp3")
    output_video_path = "test_fifth_frame_fix_output.mp4"
    
    if not os.path.exists(video_path):
        logging.error(f"视频文件不存在: {video_path}")
        return False
        
    if not os.path.exists(dub_audio_path):
        logging.error(f"配音文件不存在: {dub_audio_path}")
        return False
    
    logging.info("开始测试倒数第5帧修复方案...")
    logging.info(f"输入视频: {video_path}")
    logging.info(f"配音文件: {dub_audio_path}")
    logging.info(f"输出视频: {output_video_path}")
    
    try:
        # 1. 分析原视频信息
        logging.info("=== 步骤1: 分析原视频信息 ===")
        
        video_probe = ffmpeg.probe(video_path)
        video_duration = float(video_probe['format']['duration'])
        
        video_stream = next(s for s in video_probe['streams'] if s['codec_type'] == 'video')
        fps_str = video_stream.get('r_frame_rate', '30/1')
        fps = eval(fps_str)
        
        frame_duration = 1.0 / fps
        fifth_last_frame_time = video_duration - (5 * frame_duration)
        
        logging.info(f"原视频时长: {video_duration:.2f}秒")
        logging.info(f"视频帧率: {fps} fps")
        logging.info(f"帧间隔: {frame_duration:.4f}秒")
        logging.info(f"倒数第5帧时间点: {fifth_last_frame_time:.3f}秒")
        
        # 2. 为配音添加1秒缓冲
        logging.info("=== 步骤2: 为配音添加1秒缓冲 ===")
        
        dub_duration = get_audio_duration(dub_audio_path)
        logging.info(f"原配音时长: {dub_duration:.2f}秒")
        
        # 创建1秒静音
        one_second_silence_path = os.path.join(temp_dir, "test_silence_1s_v2.wav")
        create_silent_audio(1.0, one_second_silence_path)
        
        # 连接配音和1秒静音
        buffered_audio_path = os.path.join(temp_dir, "test_dub_buffered_1s_v2.wav")
        input_dub = ffmpeg.input(dub_audio_path)
        input_silence = ffmpeg.input(one_second_silence_path)
        concatenated_audio = ffmpeg.concat(input_dub, input_silence, v=0, a=1)
        ffmpeg.run(concatenated_audio.output(buffered_audio_path), overwrite_output=True, capture_stderr=True)
        
        buffered_duration = get_audio_duration(buffered_audio_path)
        logging.info(f"带1秒缓冲的配音时长: {buffered_duration:.2f}秒")
        
        extend_needed = buffered_duration - video_duration
        logging.info(f"需要延长视频: {extend_needed:.2f}秒")
        
        # 3. 使用修复后的 replace_audio 函数
        logging.info("=== 步骤3: 使用倒数第5帧延长视频 ===")
        
        success = replace_audio(video_path, buffered_audio_path, output_video_path)
        
        if success and os.path.exists(output_video_path):
            # 4. 验证结果
            logging.info("=== 步骤4: 验证结果 ===")
            
            output_probe = ffmpeg.probe(output_video_path)
            output_duration = float(output_probe['format']['duration'])
            
            logging.info(f"✓ 视频处理成功！")
            logging.info(f"输出视频时长: {output_duration:.2f}秒")
            logging.info(f"音频时长: {buffered_duration:.2f}秒")
            logging.info(f"原配音时长: {dub_duration:.2f}秒")
            
            # 验证时长匹配
            if abs(output_duration - buffered_duration) < 0.1:
                logging.info("✓ 时长匹配正确")
                
                # 验证包含完整配音
                if output_duration >= dub_duration + 0.9:  # 至少包含原配音 + 0.9秒缓冲
                    logging.info("✓ 包含完整配音和缓冲")
                    
                    # 检查视频流信息
                    video_streams = [s for s in output_probe['streams'] if s['codec_type'] == 'video']
                    audio_streams = [s for s in output_probe['streams'] if s['codec_type'] == 'audio']
                    
                    if video_streams and audio_streams:
                        logging.info("✓ 视频和音频流都存在")
                        logging.info(f"视频编解码器: {video_streams[0].get('codec_name', 'unknown')}")
                        logging.info(f"音频编解码器: {audio_streams[0].get('codec_name', 'unknown')}")
                        
                        # 额外信息
                        logging.info(f"延长部分将显示倒数第5帧 (时间点: {fifth_last_frame_time:.3f}秒)")
                        logging.info("这应该避免了原视频最后一帧的黑屏问题")
                        
                        return True
                    else:
                        logging.error("✗ 视频或音频流缺失")
                        return False
                else:
                    logging.error(f"✗ 输出视频时长不足以包含完整配音")
                    return False
            else:
                logging.error(f"✗ 时长不匹配，差异: {abs(output_duration - buffered_duration):.2f}秒")
                return False
        else:
            logging.error("✗ 视频处理失败")
            return False
            
    except Exception as e:
        logging.error(f"✗ 测试失败，发生异常: {e}")
        return False

if __name__ == "__main__":
    success = test_fifth_frame_fix()
    if success:
        print("\n🎉 倒数第5帧修复测试成功！")
        print("✅ 1秒缓冲已应用")
        print("✅ 视频延长使用倒数第5帧")
        print("✅ 应该解决了黑屏问题")
        print("✅ 配音完整播放")
    else:
        print("\n❌ 倒数第5帧修复测试失败")
