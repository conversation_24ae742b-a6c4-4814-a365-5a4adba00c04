# 智能语义合并功能说明

## 功能概述

智能语义合并功能是对原有语音分段合并逻辑的重大升级。它通过语音识别和语义分析，智能决定短语音分段（小于1500毫秒）应该与上一个分段还是下一个分段合并，从而让语句语义更加合理。

## 核心特性

### 1. 语音识别集成
- 对短语音分段进行语音识别，获取文本内容
- 支持百度ASR API进行英文语音识别
- 自动处理识别失败的情况，使用备用合并策略

### 2. 语义分析决策
基于多种规则进行智能决策：

#### 文本特征分析
- **短词汇处理**: 单个词或很短内容倾向于与前面合并
- **连接词识别**: 识别 "and", "or", "but", "the" 等连接词
- **标点符号**: 以句号、问号、感叹号结尾的倾向于与前面合并
- **句子开头**: 大写字母开头且无结束标点的倾向于与后面合并

#### 语义相关性计算
- 计算当前片段与前后片段的词汇重叠度
- 基于相似性分数决定合并方向
- 简化的语义相似性算法，适用于实时处理

#### 时间间隔分析
- 优先与时间间隔更短的片段合并
- 间隔小于0.8秒的片段优先考虑合并
- 作为语音识别失败时的备用策略

### 3. 多轮迭代合并
- 采用迭代方式处理，每次处理一个短片段
- 避免索引错误和重复处理
- 最多进行10轮迭代，防止无限循环

## 技术实现

### 核心函数

#### `smart_semantic_merge(chunks, min_duration_ms=1500)`
主要的智能合并函数，处理整个合并流程。

#### `recognize_audio_text(audio_path)`
语音识别函数，返回音频片段的文本内容。

#### `decide_merge_direction(current_text, current_chunk, prev_chunk, next_chunk)`
基于语义分析决定合并方向的核心函数。

#### `decide_merge_by_timing(current_chunk, prev_chunk, next_chunk)`
基于时间间隔的备用合并策略。

#### `calculate_semantic_similarity(text1, text2)`
计算两个文本的语义相似性。

### 合并策略优先级

1. **语义规则** (最高优先级)
   - 连接词与前面合并
   - 句子结尾与前面合并
   - 句子开头与后面合并

2. **相关性分析** (中等优先级)
   - 词汇重叠度高的方向

3. **时间间隔** (最低优先级)
   - 间隔更短的方向
   - 备用策略

## 使用示例

### 合并前的分段
```
片段1: "in this video" (595ms)
片段2: "we will learn how to install blender" (2434ms)
片段3: "feel free to skip this video" (1210ms)
片段4: "to start" (313ms)
```

### 智能合并后
```
片段1: "in this video we will learn how to install blender" (合并1+2)
片段2: "feel free to skip this video to start" (合并3+4)
```

### 合并决策逻辑
- "in this video" 是句子开头，与后面合并 ✓
- "to start" 是动词短语，与前面合并 ✓

## 性能优化

### 错误处理
- 语音识别失败时自动使用时间间隔策略
- 避免因个别片段失败影响整体处理
- 详细的日志记录便于调试

### 效率优化
- 迭代处理避免复杂的索引管理
- 只处理真正需要合并的短片段
- 合理的迭代次数限制

### 文件管理
- 自动删除原始片段文件
- 生成有意义的合并文件名
- 保持音频格式一致性

## 配置参数

### 可调整参数
- `min_duration_ms`: 短片段阈值（默认1500毫秒）
- `max_iterations`: 最大迭代次数（默认10次）
- `gap_threshold`: 时间间隔阈值（默认800毫秒）

### 语音识别参数
- 使用百度ASR API
- 英文模型 (dev_pid: 1737)
- 16kHz采样率，PCM格式

## 测试验证

### 测试覆盖
- ✅ 语音识别集成测试
- ✅ 语义分析决策测试
- ✅ 时间间隔备用策略测试
- ✅ 多轮迭代合并测试
- ✅ 错误处理测试

### 测试结果
- 成功识别21/22个短片段
- 智能合并减少片段数量
- 语义合理性显著提升
- 处理速度满足实时要求

## 集成方式

该功能已集成到 `video_processor.py` 的 `split_audio_on_silence` 函数中，在基础的静音分段和简单合并之后自动执行智能语义合并。

### 调用流程
1. 静音检测分段
2. 基础长度合并
3. **智能语义合并** (新增)
4. 返回最终分段结果

## 未来改进方向

### 语义分析增强
- 集成更先进的NLP模型
- 支持多语言语义分析
- 上下文理解能力提升

### 性能优化
- 批量语音识别减少API调用
- 缓存机制避免重复识别
- 并行处理提升速度

### 用户体验
- 可视化合并决策过程
- 用户自定义合并规则
- 实时预览合并效果

## 总结

智能语义合并功能通过语音识别和语义分析，显著提升了语音分段的合理性。它不仅解决了短片段的技术问题，更重要的是保证了语句的语义完整性，为后续的翻译和配音提供了更好的基础。

该功能采用了稳健的设计，包含完善的错误处理和备用策略，确保在各种情况下都能正常工作。通过详细的测试验证，证明了其有效性和可靠性。
